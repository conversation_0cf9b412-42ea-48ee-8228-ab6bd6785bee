const express = require('express');
const router = express.Router();
const customerController = require('../controllers/customerController');
const auth = require('../middleware/auth');

// Apply auth middleware to all customer routes
router.use(auth);

// Get all customers with shopping data
router.get('/', customerController.getAllCustomers);

// Get customer statistics
router.get('/stats', customerController.getCustomerStats);

// Get customers by date range
router.get('/date-range', customerController.getCustomersByDateRange);

// Get specific customer details
router.get('/:customerId', customerController.getCustomerDetails);

module.exports = router;
