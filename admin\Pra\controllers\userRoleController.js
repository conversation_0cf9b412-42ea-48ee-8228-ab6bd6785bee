const { pool } = require("../Config/Db");

const assignRole = async (req, res) => {
  const { userId, roleId } = req.body;
  if (!userId || !roleId)
    return res.status(400).json({ success: false, message: "User ID and Role ID are required" });

  try {
    const [[user], [role], [exist]] = await Promise.all([
      pool.query("SELECT id FROM users WHERE id = ?", [userId]),
      pool.query("SELECT role_id FROM admin_roles WHERE role_id = ?", [roleId]),
      pool.query("SELECT id FROM user_role_assigne WHERE user_id = ? AND role_id = ?", [userId, roleId])
    ]);

    if (!user.length) return res.status(404).json({ success: false, message: "User not found" });
    if (!role.length) return res.status(404).json({ success: false, message: "Role not found" });
    if (exist.length) return res.status(409).json({ success: false, message: "Role already assigned" });

    await pool.query("INSERT INTO user_role_assigne (user_id, role_id) VALUES (?, ?)", [userId, roleId]);
    res.status(201).json({ success: true, message: "Role assigned successfully" });
  } catch (err) {
    console.error("Assign role error:", err);
    res.status(500).json({ success: false, message: "Failed to assign role" });
  }
};

const updateRole = async (req, res) => {
  const { userId, oldRoleId, newRoleId } = req.body;

  if (!userId || !oldRoleId || !newRoleId)
    return res.status(400).json({ success: false, message: "All IDs required" });

  try {
    const [result] = await pool.query(
      "UPDATE user_role_assigne SET role_id = ? WHERE user_id = ? AND role_id = ?",
      [newRoleId, userId, oldRoleId]
    );

    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, message: "Update failed or no such assignment" });

    res.json({ success: true, message: "Role updated" });
  } catch (err) {
    console.error("Update role error:", err);
    res.status(500).json({ success: false, message: "Failed to update role" });
  }
};

const removeRole = async (req, res) => {
  const { userid, roleid } = req.params;

  if (!userid || !roleid) {
    return res.status(400).json({
      success: false,
      message: "User ID and Role ID are required"
    });
  }

  try {
    const [result] = await pool.query(
      "DELETE FROM user_role_assigne WHERE user_id = ? AND role_id = ?",
      [userid, roleid]
    );

    if (!result.affectedRows) {
      return res.status(404).json({
        success: false,
        message: "Role not found for user"
      });
    }

    res.json({ success: true, message: "Role removed successfully" });
  } catch (err) {
    console.error("Remove role error:", err);
    res.status(500).json({
      success: false,
      message: "Failed to remove role"
    });
  }
};

const getUserRoles = async (req, res) => {
  try {
    const [roles] = await pool.query(
      `SELECT r.role_id, r.role_name FROM admin_roles r
       JOIN user_role_assigne ur ON r.role_id = ur.role_id
       WHERE ur.user_id = ?`, [req.params.userid]
    );
    res.json({ success: true, data: roles });
  } catch (err) {
    console.error("Get user roles error:", err);
    res.status(500).json({ success: false, message: "Failed to fetch user roles" });
  }
};

const getUsersByRole = async (req, res) => {
  try {
    const [users] = await pool.query(
      `SELECT u.id, u.name, u.email FROM users u
       JOIN user_role_assigne ur ON u.id = ur.user_id
       WHERE ur.role_id = ?`, [req.params.roleid]
    );
    res.json({ success: true, data: users });
  } catch (err) {
    console.error("Get users by role error:", err);
    res.status(500).json({ success: false, message: "Failed to fetch users by role" });
  }
};

const getAllAssignments = async (req, res) => {
  try {
    const [assignments] = await pool.query(
      `SELECT ura.id, ura.user_id, ura.role_id, u.name AS user_name, r.role_name
       FROM user_role_assigne ura
       JOIN users u ON ura.user_id = u.id
       JOIN admin_roles r ON ura.role_id = r.role_id`
    );
    res.json({ success: true, data: assignments });
  } catch (err) {
    console.error("Get all assignments error:", err);
    res.status(500).json({ success: false, message: "Failed to fetch role assignments" });
  }
};

module.exports = {
  assignRole,
  updateRole,
  removeRole,
  getUserRoles,
  getUsersByRole,
  getAllAssignments
};
