const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const authMiddleware = require('../middleware/auth');

// Apply authentication to all order routes
router.use(authMiddleware);

// Order management routes
router.get('/', orderController.getAllOrders);
router.get('/stats', orderController.getOrderStats);
router.get('/search', orderController.searchOrders);
router.get('/export', orderController.exportOrders);
router.get('/customer/:customerId', orderController.getOrdersByCustomer);
router.get('/:orderId', orderController.getOrderById);
router.put('/:orderId/status', orderController.updateOrderStatus);

module.exports = router;
