const express = require("express");
const router = express.Router();

const categoryController = require("../controllers/categoryController");
const auth = require("../middleware/auth");       // JWT verify karne ke liye
const { isAdmin } = require("../middleware/isadmin"); // Sirf admin allow karega

// Public routes: sab categories dekh sakte hain (without auth)
router.get("/", categoryController.getAllCategories);

// Public route: ek category by id dekhna (without auth)
router.get("/:id", categoryController.getCategoryById);

// Admin only routes (category create, update, delete)
router.post("/", auth, isAdmin, categoryController.createCategory);
router.put("/:id", auth, isAdmin, categoryController.updateCategory);
router.delete("/:id", auth, isAdmin, categoryController.deleteCategory);

module.exports = router;
