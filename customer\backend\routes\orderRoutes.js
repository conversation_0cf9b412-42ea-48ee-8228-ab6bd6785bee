const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const customerAuth = require('../middleware/customerAuth');

// Protected routes - require authentication
router.use(customerAuth);

// Place order
router.post('/place', orderController.placeOrder);

// Get customer orders
router.get('/customer/:customer_id', orderController.getOrdersByCustomer);

// Get order details
router.get('/:order_id', orderController.getOrderDetails);

// Update order status
router.put('/:order_id/status', orderController.updateOrderStatus);

module.exports = router;
