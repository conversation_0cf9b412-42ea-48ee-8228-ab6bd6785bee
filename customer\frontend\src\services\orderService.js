import axiosInstance from '../utils/axiosInstance';

const orderService = {
  // Place order
  placeOrder: async (orderData) => {
    try {
      const response = await axiosInstance.post('/orders/place', orderData);
      return response.data;
    } catch (error) {
      console.error('❌ Place order error:', error);
      throw error;
    }
  },

  // Get customer orders
  getOrders: async (page = 1, limit = 10) => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const customerId = user.id;
      
      if (!customerId) {
        throw new Error('Customer ID not found');
      }

      const response = await axiosInstance.get(`/orders/customer/${customerId}?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('❌ Get orders error:', error);
      throw error;
    }
  },

  // Get order details
  getOrderDetails: async (orderId) => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const customerId = user.id;
      
      if (!customerId) {
        throw new Error('Customer ID not found');
      }

      const response = await axiosInstance.get(`/orders/${orderId}?customer_id=${customerId}`);
      return response.data;
    } catch (error) {
      console.error('❌ Get order details error:', error);
      throw error;
    }
  },

  // Update order status
  updateOrderStatus: async (orderId, status, message = '') => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const customerId = user.id;
      
      if (!customerId) {
        throw new Error('Customer ID not found');
      }

      const response = await axiosInstance.put(`/orders/${orderId}/status?customer_id=${customerId}`, {
        status,
        message
      });
      return response.data;
    } catch (error) {
      console.error('❌ Update order status error:', error);
      throw error;
    }
  }
};

export default orderService; 