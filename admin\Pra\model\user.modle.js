const { pool } = require('../Config/Db');

// User model with CRUD operations
class User {
  constructor(user) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.phone = user.phone;
    this.status = user.status || 'active';
    this.created_at = user.created_at;
    this.updated_at = user.updated_at;
  }

  // Get all users
  static async getAll() {
    try {
      const [rows] = await pool.query('SELECT * FROM users ORDER BY created_at DESC');
      return rows;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  // Get user by ID
  static async getById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE id = ?', [id]);
      return rows[0];
    } catch (error) {
      console.error(`Error fetching user with ID ${id}:`, error);
      throw error;
    }
  }

  // Create a new user
  static async create(newUser) {
    try {
      const query = 'INSERT INTO users (name, email, phone, password, status) VALUES (?, ?, ?, ?, ?)';
      const [result] = await pool.query(query, [
        newUser.name, 
        newUser.email, 
        newUser.phone, 
        newUser.password,
        newUser.status || 'active'
      ]);

      return { id: result.insertId, ...newUser };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Update an existing user
  static async update(id, user) {
    try {
      const query = 'UPDATE users SET name = ?, email = ?, phone = ?, status = ? WHERE id = ?';
      const [result] = await pool.query(query, [
        user.name, 
        user.email, 
        user.phone, 
        user.status || 'active',
        id
      ]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }

  // Delete a user
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM users WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  }
}

module.exports = User;