const User = require("../model/user.modle");

const userController = {
  getAllUsers: async (req, res) => {
    try {
      const users = await User.getAll();
      res.json({ success: true, data: users });
    } catch (err) {
      res.status(500).json({ success: false, message: "Failed to retrieve users" });
    }
  },

  getUserById: async (req, res) => {
    try {
      const user = await User.getById(req.params.id);
      if (!user) return res.status(404).json({ success: false, message: "User not found" });
      res.json({ success: true, data: user });
    } catch {
      res.status(500).json({ success: false, message: "Failed to retrieve user" });
    }
  },

  createUser: async (req, res) => {
    try {
      const { name, email, phone, password, status } = req.body;
      if (!name || !email || !password) {
        return res.status(400).json({ 
          success: false, 
          message: "Name, email, and password are required" 
        });
      }

      const newUser = await User.create({ 
        name, 
        email, 
        phone: phone || null, 
        password,
        status: status || 'active'
      });
      res.status(201).json({ success: true, message: "User created", data: newUser });
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(500).json({ success: false, message: "Failed to create user" });
    }
  },

  updateUser: async (req, res) => {
    try {
      const id = req.params.id;
      const existing = await User.getById(id);
      if (!existing) return res.status(404).json({ success: false, message: "User not found" });

      const data = {
        name: req.body.name || existing.name,
        email: req.body.email || existing.email,
        phone: req.body.phone ?? existing.phone,
        status: req.body.status || existing.status || 'active',
      };

      const updated = await User.update(id, data);
      res.json(updated
        ? { success: true, message: "User updated", data: { id, ...data } }
        : { success: false, message: "Update failed" });
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ success: false, message: "Failed to update user" });
    }
  },

  deleteUser: async (req, res) => {
    try {
      const user = await User.getById(req.params.id);
      if (!user) return res.status(404).json({ success: false, message: "User not found" });

      const deleted = await User.delete(req.params.id);
      res.json(deleted
        ? { success: true, message: "User deleted" }
        : { success: false, message: "Delete failed" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ success: false, message: "Failed to delete user" });
    }
  },
};

module.exports = userController;