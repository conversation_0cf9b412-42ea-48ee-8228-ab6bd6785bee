const { pool } = require("../Config/Db");

const Product = {
  create: async (data) => {
    try {
      console.log("Creating product with data:", data);
      const [result] = await pool.query(
        `INSERT INTO products (
          name, description, price, quantity, 
          category_id, subcategory_id, images, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          data.name, 
          data.description, 
          data.price, 
          data.quantity || 0,
          data.category_id,
          data.subcategory_id, 
          JSON.stringify(data.images || []),
          data.status || 'active'
        ]
      );
      console.log("Product created successfully:", result);
      return result;
    } catch (error) {
      console.error("Error creating product:", error);
      throw error;
    }
  },

  getAll: async () => {
    try {
      console.log("Fetching all products");
      const [rows] = await pool.query(`
        SELECT 
          p.*,
          s.name AS subcategory_name,
          c.name AS category_name
        FROM products p
        LEFT JOIN subcategories s ON p.subcategory_id = s.id
        LEFT JOIN categories c ON s.category_id = c.id
        ORDER BY p.created_at DESC
      `);
      
      console.log("Products fetched:", rows.length);
      
      // Parse images JSON for each product with error handling
      return rows.map(product => {
        let images = [];
        if (product.images) {
          try {
            images = JSON.parse(product.images);
          } catch (parseError) {
            console.warn(`Invalid JSON in images for product ${product.id}:`, product.images);
            images = [];
          }
        }
        return {
          ...product,
          images: images
        };
      });
    } catch (error) {
      console.error("Error fetching products:", error);
      throw error;
    }
  },

  getById: async (id) => {
    try {
      console.log("Fetching product by ID:", id);
      const [rows] = await pool.query(`
        SELECT 
          p.*,
          s.name AS subcategory_name,
          c.name AS category_name
        FROM products p
        LEFT JOIN subcategories s ON p.subcategory_id = s.id
        LEFT JOIN categories c ON s.category_id = c.id
        WHERE p.id = ?
      `, [id]);
      
      if (rows.length > 0) {
        const product = rows[0];
        let images = [];
        if (product.images) {
          try {
            images = JSON.parse(product.images);
          } catch (parseError) {
            console.warn(`Invalid JSON in images for product ${product.id}:`, product.images);
            images = [];
          }
        }
        return {
          ...product,
          images: images
        };
      }
      return null;
    } catch (error) {
      console.error("Error fetching product by ID:", error);
      throw error;
    }
  },

  update: async (id, data) => {
    try {
      console.log("Updating product:", id, "with data:", data);
      const [result] = await pool.query(
        `UPDATE products SET 
          name = ?, description = ?, price = ?, quantity = ?,
          category_id = ?, subcategory_id = ?, images = ?, status = ?
         WHERE id = ?`,
        [
          data.name, 
          data.description, 
          data.price, 
          data.quantity || 0,
          data.category_id,
          data.subcategory_id, 
          JSON.stringify(data.images || []),
          data.status || 'active',
          id
        ]
      );
      console.log("Product updated successfully:", result);
      return result;
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    }
  },

  delete: async (id) => {
    try {
      console.log("Deleting product:", id);
      const [result] = await pool.query("DELETE FROM products WHERE id = ?", [id]);
      console.log("Product deleted successfully:", result);
      return result;
    } catch (error) {
      console.error("Error deleting product:", error);
      throw error;
    }
  },

  // Get products by category
  getByCategory: async (categoryId) => {
    try {
      const [rows] = await pool.query(`
        SELECT 
          p.*, 
          s.name AS subcategory_name, 
          c.name AS category_name
        FROM products p
        LEFT JOIN subcategories s ON p.subcategory_id = s.id
        LEFT JOIN categories c ON s.category_id = c.id
        WHERE c.id = ?
        ORDER BY p.created_at DESC
      `, [categoryId]);
      
      return rows.map(product => {
        let images = [];
        if (product.images) {
          try {
            images = JSON.parse(product.images);
          } catch (parseError) {
            console.warn(`Invalid JSON in images for product ${product.id}:`, product.images);
            images = [];
          }
        }
        return {
          ...product,
          images: images
        };
      });
    } catch (error) {
      console.error("Error fetching products by category:", error);
      throw error;
    }
  },

  // Get products by subcategory
  getBySubcategory: async (subcategoryId) => {
    try {
      const [rows] = await pool.query(`
        SELECT 
          p.*, 
          s.name AS subcategory_name, 
          c.name AS category_name
        FROM products p
        LEFT JOIN subcategories s ON p.subcategory_id = s.id
        LEFT JOIN categories c ON s.category_id = c.id
        WHERE s.id = ?
        ORDER BY p.created_at DESC
      `, [subcategoryId]);
      
      return rows.map(product => {
        let images = [];
        if (product.images) {
          try {
            images = JSON.parse(product.images);
          } catch (parseError) {
            console.warn(`Invalid JSON in images for product ${product.id}:`, product.images);
            images = [];
          }
        }
        return {
          ...product,
          images: images
        };
      });
    } catch (error) {
      console.error("Error fetching products by subcategory:", error);
      throw error;
    }
  },

  // Get product with details
  getWithDetails: async (id) => {
    try {
      console.log("Fetching product with details by ID:", id);
      const [rows] = await pool.query(`
        SELECT 
          p.*,
          s.name AS subcategory_name,
          c.name AS category_name,
          pd.brand,
          pd.model,
          pd.detailed_description,
          pd.specifications,
          pd.features,
          pd.warranty,
          pd.return_policy,
          pd.shipping_info,
          pd.reviews_count,
          pd.average_rating,
          pd.sale_price,
          pd.discount_percentage
        FROM products p
        LEFT JOIN subcategories s ON p.subcategory_id = s.id
        LEFT JOIN categories c ON s.category_id = c.id
        LEFT JOIN product_details pd ON p.id = pd.product_id
        WHERE p.id = ?
      `, [id]);
      
      if (rows.length > 0) {
        const product = rows[0];
        let images = [];
        if (product.images) {
          try {
            images = JSON.parse(product.images);
          } catch (parseError) {
            console.warn(`Invalid JSON in images for product ${product.id}:`, product.images);
            images = [];
          }
        }
        
        // Parse specifications if it's a string
        let specifications = null;
        if (product.specifications && typeof product.specifications === 'string') {
          try {
            specifications = JSON.parse(product.specifications);
          } catch (parseError) {
            console.warn(`Invalid JSON in specifications for product ${product.id}:`, product.specifications);
            specifications = null;
          }
        }
        
        return {
          ...product,
          images: images,
          specifications: specifications
        };
      }
      return null;
    } catch (error) {
      console.error("Error fetching product with details:", error);
      throw error;
    }
  }
};

module.exports = Product;
