const { pool } = require('../config/db');
const cartModel = require('../models/cartModel');

const orderController = {
  // Place order
  placeOrder: async (req, res) => {
    try {
      const { shipping_address, billing_address, payment_method } = req.body;
      const customer_id = req.customer.id; // Get customer ID from auth middleware

      if (!shipping_address || !billing_address) {
        return res.status(400).json({
          success: false,
          message: 'Shipping address and billing address are required'
        });
      }

      // Get customer's cart
      const cart = await cartModel.getCart(customer_id);
      if (!cart || cart.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Cart is empty'
        });
      }

      // Calculate total amount
      const total_amount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      // Generate order number
      const order_number = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`;

      // Create order
      const [orderResult] = await pool.execute(
        'INSERT INTO orders (order_number, customer_id, total_amount, shipping_address, billing_address, payment_method, status, payment_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [order_number, customer_id, total_amount, shipping_address, billing_address, payment_method || 'razorpay', 'pending', 'pending']
      );

      const order_id = orderResult.insertId;
      console.log('✅ Order created with ID:', order_id);

      // Add order items
      for (const item of cart) {
        await pool.execute(
          'INSERT INTO order_items (order_id, product_id, product_name, quantity, product_price, total_price) VALUES (?, ?, ?, ?, ?, ?)',
          [order_id, item.product_id, item.name, item.quantity, item.price, item.price * item.quantity]
        );
      }

      console.log('✅ Order items added successfully');

      // Clear cart
      await cartModel.clearCart(customer_id);
      console.log('✅ Cart cleared successfully');

      res.status(201).json({
        success: true,
        message: 'Order placed successfully',
        data: {
          orderId: order_id,
          order_id,
          orderNumber: order_number,
          order_number,
          total_amount,
          status: 'pending'
        }
      });

    } catch (error) {
      console.error('❌ Place order error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to place order',
        error: error.message
      });
    }
  },

  // Get orders by customer
  getOrdersByCustomer: async (req, res) => {
    try {
      const { customer_id } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      // Get orders with pagination
      const [orders] = await pool.query(
        `SELECT o.*,
                COUNT(oi.id) as item_count,
                GROUP_CONCAT(oi.product_name SEPARATOR ', ') as products
         FROM orders o
         LEFT JOIN order_items oi ON o.id = oi.order_id
         WHERE o.customer_id = ?
         GROUP BY o.id
         ORDER BY o.created_at DESC
         LIMIT ${limit} OFFSET ${offset}`,
        [customer_id]
      );

      // Get total count
      const [countResult] = await pool.query(
        'SELECT COUNT(*) as total FROM orders WHERE customer_id = ?',
        [customer_id]
      );

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      res.status(200).json({
        success: true,
        message: 'Orders retrieved successfully',
        data: {
          orders,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        }
      });

    } catch (error) {
      console.error('❌ Get orders error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get orders',
        error: error.message
      });
    }
  },

  // Get order details
  getOrderDetails: async (req, res) => {
    try {
      const { order_id } = req.params;
      const { customer_id } = req.query;

      // Get order details
      const [orders] = await pool.execute(
        'SELECT * FROM orders WHERE id = ? AND customer_id = ?',
        [order_id, customer_id]
      );

      if (orders.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Order not found'
        });
      }

      const order = orders[0];

      // Get order items
      const [items] = await pool.execute(
        'SELECT * FROM order_items WHERE order_id = ?',
        [order_id]
      );

      // Get payment details
      const [payments] = await pool.execute(
        'SELECT * FROM order_payments WHERE order_id = ?',
        [order_id]
      );

      res.status(200).json({
        success: true,
        message: 'Order details retrieved successfully',
        data: {
          ...order,
          items,
          payment: payments[0] || null
        }
      });

    } catch (error) {
      console.error('❌ Get order details error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get order details',
        error: error.message
      });
    }
  },

  // Update order status
  updateOrderStatus: async (req, res) => {
    try {
      const { order_id } = req.params;
      const { status, message } = req.body;
      const { customer_id } = req.query;

      if (!status) {
        return res.status(400).json({
          success: false,
          message: 'Status is required'
        });
      }

      // Validate status
      const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid status'
        });
      }

      // Update order status
      const [result] = await pool.execute(
        'UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ? AND customer_id = ?',
        [status, order_id, customer_id]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Order not found or unauthorized'
        });
      }

      // Add status history if message provided
      if (message) {
        await pool.execute(
          'INSERT INTO order_status_history (order_id, status, message) VALUES (?, ?, ?)',
          [order_id, status, message]
        );
      }

      res.status(200).json({
        success: true,
        message: 'Order status updated successfully',
        data: { status }
      });

    } catch (error) {
      console.error('❌ Update order status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update order status',
        error: error.message
      });
    }
  }
};

module.exports = orderController; 
