const express = require("express");
const router = express.Router();
const auth = require("../middleware/auth");

const {
  createSubcategory,
  getAllSubcategories,
  getByCategoryId, // ✅ yeh line likhna zaroori hai
  updateSubcategory,
  deleteSubcategory,
} = require("../controllers/subcategoryController");

// Public routes (without auth)
router.get("/", getAllSubcategories);
router.get("/category/:categoryId", getByCategoryId); // ✅ ab yeh kaam karega

// Admin only routes (with auth)
router.post("/", auth, createSubcategory);
router.put("/:id", auth, updateSubcategory);
router.delete("/:id", auth, deleteSubcategory);

module.exports = router;
