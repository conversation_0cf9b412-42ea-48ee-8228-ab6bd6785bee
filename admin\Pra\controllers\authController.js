const { pool } = require("../Config/Db");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { addToBlacklist, isBlacklisted } = require('../utils/tokenBlacklist');


const authController = {
  register: async (req, res) => {
    try {
      const { name, email, password, phone, role_id, status } = req.body;

      if (!name || !email || !password) {
        return res.status(400).json({
          success: false,
          message: "Name, email, and password are required",
        });
      }

      const [exists] = await pool.query("SELECT id FROM users WHERE email = ?", [email]);
      if (exists.length) {
        return res.status(409).json({
          success: false,
          message: "User already exists",
        });
      }

      const hashed = await bcrypt.hash(password, 10);

      const [result] = await pool.query(
        "INSERT INTO users (name, email, password, phone, status) VALUES (?, ?, ?, ?, ?)",
        [name, email, hashed, phone || null, status || 'active']
      );

      const userId = result.insertId;

      // Assign role if provided
      if (role_id) {
        await pool.query(
          "INSERT INTO user_role_assigne (user_id, role_id) VALUES (?, ?)",
          [userId, role_id]
        );
      } else {
        // Assign default user role if no role specified
        const [defaultRole] = await pool.query("SELECT role_id FROM admin_roles WHERE role_name = 'user' LIMIT 1");
        if (defaultRole.length > 0) {
          await pool.query(
            "INSERT INTO user_role_assigne (user_id, role_id) VALUES (?, ?)",
            [userId, defaultRole[0].role_id]
          );
        }
      }

      res.status(201).json({
        success: true,
        message: "User registered successfully",
        userId,
      });
    } catch (err) {
      console.error("Register Error:", err);
      res.status(500).json({
        success: false,
        message: "Registration failed",
        error: err.message,
      });
    }
  },

  login: async (req, res) => {
    try {
      const { email, password, rememberMe } = req.body;
      if (!email || !password) {
        return res.status(400).json({ success: false, message: "Email and password are required" });
      }

      const [users] = await pool.query("SELECT * FROM users WHERE email = ?", [email]);
      if (!users.length || !(await bcrypt.compare(password, users[0].password))) {
        return res.status(401).json({ success: false, message: "Invalid credentials" });
      }

      const user = users[0];

      // Check if user is active
      if (user.status === 'inactive') {
        return res.status(401).json({ success: false, message: "Account is deactivated" });
      }

      const [roles] = await pool.query(
        `SELECT r.role_name FROM user_role_assigne ur
         JOIN admin_roles r ON ur.role_id = r.role_id
         WHERE ur.user_id = ?`,
        [user.id]
      );

      const userRoles = roles.map(r => r.role_name);

      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          roles: userRoles,
        },
        process.env.JWT_SECRET,
        {
          expiresIn: rememberMe ? "7d" : "24h",
        }
      );

      res.cookie("token", token, {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 30 * 60 * 1000,
      });

      res.json({
        success: true,
        message: "Login successful",
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          roles: userRoles,
        },
      });
    } catch (err) {
      console.error("Login Error:", err);
      res.status(500).json({ success: false, message: "Login failed", error: err.message });
    }
  },

  logout: async (req, res) => {
    try {
      const token = req.headers.authorization?.split(" ")[1] || req.cookies?.token;
      if (token) await addToBlacklist(token);

      res.clearCookie("token", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });

      return res.status(200).json({ success: true, message: "Logged out successfully" });
    } catch (error) {
      console.error("Logout Error:", error);
      return res.status(500).json({ success: false, message: "Logout failed", error: error.message });
    }
  },

  validateToken: async (req, res) => {
    try {
      const token = req.headers.authorization?.split(" ")[1] || req.cookies?.token;
      
      if (!token) {
        return res.status(401).json({ 
          success: false, 
          message: "No token provided",
          isValid: false 
        });
      }

      // Check if token is blacklisted
      if (await isBlacklisted(token)) {
        return res.status(401).json({ 
          success: false, 
          message: "Token is blacklisted",
          isValid: false 
        });
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Check if user still exists and is active
      const [users] = await pool.query(
        "SELECT id, name, email, status FROM users WHERE id = ?", 
        [decoded.id]
      );

      if (!users.length) {
        return res.status(401).json({ 
          success: false, 
          message: "User not found",
          isValid: false 
        });
      }

      const user = users[0];
      if (user.status === 'inactive') {
        return res.status(401).json({ 
          success: false, 
          message: "User account is deactivated",
          isValid: false 
        });
      }

      // Get user roles
      const [roles] = await pool.query(
        `SELECT r.role_name FROM user_role_assigne ur
         JOIN admin_roles r ON ur.role_id = r.role_id
         WHERE ur.user_id = ?`,
        [user.id]
      );

      const userRoles = roles.map(r => r.role_name);

      res.json({
        success: true,
        message: "Token is valid",
        isValid: true,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          roles: userRoles,
        },
      });
    } catch (err) {
      console.error("Token validation error:", err);
      res.status(401).json({ 
        success: false, 
        message: "Invalid token",
        isValid: false 
      });
    }
  },

  getCurrentUser: async (req, res) => {
    // Refactored to use async/await for consistency with other controller functions.
    try {
      const { id } = req.user;
      const [users] = await pool.query("SELECT id, name, email, phone, status FROM users WHERE id = ?", [id]);
      
      if (!users.length) {
        return res.status(404).json({ success: false, message: "User not found" });
      }
      
      res.json({ success: true, user: users[0] });
    } catch (err) {
      res.status(500).json({ success: false, message: "Failed to get user", error: err.message });
    }
  },
};

module.exports = authController;
