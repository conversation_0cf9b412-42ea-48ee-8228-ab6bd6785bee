const OrderModel = require('../model/orderModel');

const orderController = {
  // Get all orders
  getAllOrders: async (req, res) => {
    try {
      const page = parseInt(req?.query?.page) || 1;
      const limit = parseInt(req?.query?.limit) || 20;
      const status = req.query.status || null;

      const result = await OrderModel.getAllOrders(page, limit, status);
      
      if (!result || result.length === 0) {
        return res.status(200).json({
          success: true,
          message: "No orders found",
          data: [],
          pagination: {
            page,
            limit,
            total: 0,
            pages: 0
          }
        });
      }

      res.status(200).json({
        success: true,
        message: "Orders retrieved successfully",
        data: result.orders || result,
        pagination: result.pagination || {
          page,
          limit,
          total: result.length || 0,
          pages: Math.ceil((result.length || 0) / limit)
        }
      });
    } catch (error) {
      console.error("Get all orders error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get orders"
      });
    }
  },

  // Get order by ID
  getOrderById: async (req, res) => {
    try {
      const orderId = req?.params?.orderId;

      const order = await OrderModel.getOrderById(orderId);
      
      if (!order) {
        return res.status(404).json({
          success: false,
          message: "Order not found"
        });
      }

      res.status(200).json({
        success: true,
        message: "Order details retrieved successfully",
        data: order
      });
    } catch (error) {
      console.error("Get order by ID error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get order details"
      });
    }
  },

  // Update order status
  updateOrderStatus: async (req, res) => {
    try {
      const orderId = req.params.orderId;
      const { status, message } = req.body;
      const adminId = req.user?.id; // Assuming admin auth middleware sets req.user

      if (!status) {
        return res.status(400).json({
          success: false,
          message: "Status is required"
        });
      }

      // Validate status
      const validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          success: false,
          message: "Invalid status"
        });
      }

      const success = await OrderModel.updateOrderStatus(orderId, status, message, adminId);
      
      if (!success) {
        return res.status(400).json({
          success: false,
          message: "Failed to update order status"
        });
      }

      res.status(200).json({
        success: true,
        message: "Order status updated successfully"
      });
    } catch (error) {
      console.error("Update order status error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update order status"
      });
    }
  },

  // Get orders by customer ID
  getOrdersByCustomer: async (req, res) => {
    try {
      const customerId = req.params.customerId;
      const page = parseInt(req?.query?.page) || 1;
      const limit = parseInt(req?.query?.limit) || 20;

      const result = await OrderModel.getOrdersByCustomer(customerId, page, limit);
      
      res.status(200).json({
        success: true,
        message: "Customer orders retrieved successfully",
        data: result.orders || result,
        pagination: result.pagination || {
          page,
          limit,
          total: result.length || 0,
          pages: Math.ceil((result.length || 0) / limit)
        }
      });
    } catch (error) {
      console.error("Get customer orders error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get customer orders"
      });
    }
  },

  // Get order statistics
  getOrderStats: async (req, res) => {
    try {
      const stats = await OrderModel.getOrderStats();
      
      res.status(200).json({
        success: true,
        message: "Order statistics retrieved successfully",
        data: stats
      });
    } catch (error) {
      console.error("Get order stats error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get order statistics"
      });
    }
  },

  // Search orders
  searchOrders: async (req, res) => {
    try {
      const { query, status, startDate, endDate, minAmount, maxAmount } = req.query;
      const page = parseInt(req?.query?.page) || 1;
      const limit = parseInt(req?.query?.limit) || 20;

      const result = await OrderModel.searchOrders({
        query,
        status,
        startDate,
        endDate,
        minAmount,
        maxAmount
      }, page, limit);

      res.status(200).json({
        success: true,
        message: "Orders search completed successfully",
        data: result.orders || result,
        pagination: result.pagination || {
          page,
          limit,
          total: result.length || 0,
          pages: Math.ceil((result.length || 0) / limit)
        }
      });
    } catch (error) {
      console.error("Search orders error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to search orders"
      });
    }
  },

  // Export orders
  exportOrders: async (req, res) => {
    try {
      const { format = 'csv', status, startDate, endDate } = req.query;
      
      const orders = await OrderModel.exportOrders({ status, startDate, endDate });
      
      if (format === 'csv') {
        // Generate CSV
        const csv = generateCSV(orders);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=orders.csv');
        res.send(csv);
      } else if (format === 'json') {
        res.status(200).json({
          success: true,
          message: "Orders exported successfully",
          data: orders
        });
      } else {
        res.status(400).json({
          success: false,
          message: "Invalid export format"
        });
      }
    } catch (error) {
      console.error("Export orders error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to export orders"
      });
    }
  }
};

// Helper function to generate CSV
const generateCSV = (orders) => {
  const headers = ['Order ID', 'Order Number', 'Customer', 'Total Amount', 'Status', 'Payment Method', 'Created Date'];
  const rows = orders.map(order => [
    order.id,
    order.order_number,
    order.customer_name || 'N/A',
    order.total_amount,
    order.status,
    order.payment_method,
    new Date(order.created_at).toLocaleDateString()
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

module.exports = orderController;
