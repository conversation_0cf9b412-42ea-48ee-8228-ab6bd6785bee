const Product = require("../model/productModel");

exports.createProduct = async (req, res) => {
  try {
    // Handle image URLs from request body
    const images = req.body.images ? (Array.isArray(req.body.images) ? req.body.images : [req.body.images]) : [];

    const productData = {
      ...req?.body,
      images: images,
      price: parseFloat(req?.body?.price),
      quantity: parseInt(req.body.quantity) || 0,
      status: req.body.status || 'active'
    };

    const result = await Product.create(productData);
    res.status(201).json({ 
      success: true, 
      message: "Product created successfully", 
      productId: result.insertId 
    });
  } catch (err) {
    console.error("Create product error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error creating product"
    });
  }
};

exports.getAllProducts = async (req, res) => {
  try {
    const products = await Product.getAll();
    res.status(200).json({ 
      success: true, 
      data: products 
    });
  } catch (err) {
    console.error("Get all products error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error fetching products"
    });
  }
};

exports.getProductById = async (req, res) => {
  try {
    const product = await Product.getById(req.params.id);
    if (!product) {
      return res.status(404).json({ 
        success: false, 
        message: "Product not found" 
      });
    }
    res.status(200).json({ 
      success: true, 
      data: product 
    });
  } catch (err) {
    console.error("Get product by ID error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error fetching product"
    });
  }
};

exports.updateProduct = async (req, res) => {
  try {
    // Handle image URLs from request body
    const images = req.body.images ? (Array.isArray(req.body.images) ? req.body.images : [req.body.images]) : [];

    const productData = {
      ...req.body,
      images: images,
      price: parseFloat(req.body.price),
      quantity: parseInt(req.body.quantity) || 0
    };

    await Product.update(req.params.id, productData);
    res.status(200).json({ 
      success: true, 
      message: "Product updated successfully" 
    });
  } catch (err) {
    console.error("Update product error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error updating product"
    });
  }
};

exports.deleteProduct = async (req, res) => {
  try {
    await Product.delete(req.params.id);
    res.status(200).json({ 
      success: true, 
      message: "Product deleted successfully" 
    });
  } catch (err) {
    console.error("Delete product error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error deleting product"
    });
  }
};

// Get products by category
exports.getProductsByCategory = async (req, res) => {
  try {
    const products = await Product.getByCategory(req.params.categoryId);
    res.status(200).json({ 
      success: true, 
      data: products 
    });
  } catch (err) {
    console.error("Get products by category error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error fetching products by category"
    });
  }
};

// Get products by subcategory
exports.getProductsBySubcategory = async (req, res) => {
  try {
    const products = await Product.getBySubcategory(req.params.subcategoryId);
    res.status(200).json({ 
      success: true, 
      data: products 
    });
  } catch (err) {
    console.error("Get products by subcategory error:", err);
    res.status(500).json({ 
      success: false, 
      message: "Error fetching products by subcategory"
    });
  }
};
