const { pool } = require('../Config/Db');

// Get all customers with their shopping data
const getAllCustomers = async (req, res) => {
  try {
    const query = `
      SELECT 
        c.id,
        c.name,
        c.email,
        c.phone,
        c.created_at,
        COUNT(DISTINCT o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_spent,
        MAX(o.created_at) as last_order_date
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
      GROUP BY c.id, c.name, c.email, c.phone, c.created_at
      ORDER BY total_spent DESC, total_orders DESC
    `;

    const [results] = await pool.query(query);

    res.status(200).json({
      success: true,
      message: 'Customers fetched successfully',
      customers: results
    });
  } catch (error) {
    console.error('Error in getAllCustomers:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get customer details with recent orders
const getCustomerDetails = async (req, res) => {
  try {
    const { customerId } = req.params;

    // Get customer basic info
    const customerQuery = `
      SELECT 
        c.id,
        c.name,
        c.email,
        c.phone,
        c.created_at,
        COUNT(DISTINCT o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_spent,
        MAX(o.created_at) as last_order_date
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
      WHERE c.id = ?
      GROUP BY c.id, c.name, c.email, c.phone, c.created_at
    `;

    // Get recent orders
    const ordersQuery = `
      SELECT 
        o.id,
        o.total_amount,
        o.status,
        o.created_at,
        o.payment_method
      FROM orders o
      WHERE o.customer_id = ?
      ORDER BY o.created_at DESC
      LIMIT 10
    `;

    const [customerResults] = await pool.query(customerQuery, [customerId]);

    if (customerResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    const customer = customerResults[0];

    // Fetch recent orders
    const [orderResults] = await pool.query(ordersQuery, [customerId]);
    customer.recent_orders = orderResults;

    res.status(200).json({
      success: true,
      message: 'Customer details fetched successfully',
      customer: customer
    });
  } catch (error) {
    console.error('Error in getCustomerDetails:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get customer statistics
const getCustomerStats = async (req, res) => {
  try {
    const statsQuery = `
      SELECT 
        COUNT(DISTINCT c.id) as total_customers,
        COUNT(DISTINCT CASE WHEN o.id IS NOT NULL THEN c.id END) as active_customers,
        COUNT(DISTINCT o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_revenue,
        AVG(o.total_amount) as avg_order_value
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
    `;

    const [results] = await pool.query(statsQuery);

    res.status(200).json({
      success: true,
      message: 'Customer statistics fetched successfully',
      stats: results[0]
    });
  } catch (error) {
    console.error('Error in getCustomerStats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

// Get customers by date range
const getCustomersByDateRange = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let query = `
      SELECT 
        c.id,
        c.name,
        c.email,
        c.phone,
        c.created_at,
        COUNT(DISTINCT o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_spent,
        MAX(o.created_at) as last_order_date
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
      WHERE 1=1
    `;

    const params = [];

    if (startDate && endDate) {
      query += ` AND c.created_at BETWEEN ? AND ?`;
      params.push(startDate, endDate);
    } else if (startDate) {
      query += ` AND c.created_at >= ?`;
      params.push(startDate);
    } else if (endDate) {
      query += ` AND c.created_at <= ?`;
      params.push(endDate);
    }

    query += `
      GROUP BY c.id, c.name, c.email, c.phone, c.created_at
      ORDER BY c.created_at DESC
    `;

    const [results] = await pool.query(query, params);

    res.status(200).json({
      success: true,
      message: 'Customers fetched successfully',
      customers: results
    });
  } catch (error) {
    console.error('Error in getCustomersByDateRange:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

module.exports = {
  getAllCustomers,
  getCustomerDetails,
  getCustomerStats,
  getCustomersByDateRange
};
