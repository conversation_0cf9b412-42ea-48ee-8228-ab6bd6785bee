const db = require('../config/db');

const cartModel = {
  // Add item to cart
  addToCart: async (customerId, productId, quantity = 1) => {
    try {
      // Validate product exists
      const [products] = await db.query(
        'SELECT id FROM products WHERE id = ?',
        [productId]
      );
      if (products.length === 0) {
        const error = new Error('Product not found');
        error.statusCode = 404;
        throw error;
      }

      if (Number(quantity) <= 0) {
        const error = new Error('Quantity must be greater than 0');
        error.statusCode = 400;
        throw error;
      }

      // Check if item already exists in cart
      const [existing] = await db.query(
        'SELECT * FROM cart WHERE customer_id = ? AND product_id = ?',
        [customerId, productId]
      );

      if (existing.length > 0) {
        // Update quantity
        const newQuantity = existing[0].quantity + Number(quantity);
        const [result] = await db.query(
          'UPDATE cart SET quantity = ? WHERE customer_id = ? AND product_id = ?',
          [newQuantity, customerId, productId]
        );
        return result.affectedRows > 0;
      } else {
        // Add new item
        const [result] = await db.query(
          'INSERT INTO cart (customer_id, product_id, quantity) VALUES (?, ?, ?)',
          [customerId, productId, quantity]
        );
        return result.affectedRows > 0;
      }
    } catch (error) {
      console.error('❌ Add to cart error:', error);
      throw error;
    }
  },

  // Get cart items
  getCart: async (customerId) => {
    try {
      const [rows] = await db.query(
        `SELECT c.*, p.name, p.price 
         FROM cart c
         JOIN products p ON c.product_id = p.id
         WHERE c.customer_id = ?`,
        [customerId]
      );
      return rows;
    } catch (error) {
      console.error('❌ Get cart error:', error);
      throw error;
    }
  },

  // Remove item from cart
  removeFromCart: async (customerId, productId) => {
    try {
      const [result] = await db.query(
        'DELETE FROM cart WHERE customer_id = ? AND product_id = ?',
        [customerId, productId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('❌ Remove from cart error:', error);
      throw error;
    }
  },

  // Update quantity
  updateQuantity: async (customerId, productId, quantity) => {
    try {
      if (quantity <= 0) {
        return await cartModel.removeFromCart(customerId, productId);
      }

      const [result] = await db.query(
        'UPDATE cart SET quantity = ? WHERE customer_id = ? AND product_id = ?',
        [quantity, customerId, productId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('❌ Update quantity error:', error);
      throw error;
    }
  },

  // Clear cart
  clearCart: async (customerId) => {
    try {
      const [result] = await db.query(
        'DELETE FROM cart WHERE customer_id = ?',
        [customerId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('❌ Clear cart error:', error);
      throw error;
    }
  },

  // Get cart count
  getCartCount: async (customerId) => {
    try {
      const [rows] = await db.query(
        'SELECT COUNT(*) as count FROM cart WHERE customer_id = ?',
        [customerId]
      );
      return rows[0].count;
    } catch (error) {
      console.error('❌ Get cart count error:', error);
      throw error;
    }
  }
};

module.exports = cartModel;
