const { pool } = require("../Config/Db");

const Subcategory = {
  // Get all subcategories with category names
  getAll: async () => {
    const [rows] = await pool.query(`
      SELECT 
        s.*,
        c.name as category_name,
        (SELECT COUNT(*) FROM products WHERE subcategory_id = s.id) as product_count
      FROM subcategories s
      LEFT JOIN categories c ON s.category_id = c.id
      ORDER BY s.created_at DESC
    `);
    return rows;
  },

  // Get subcategories by category ID with category names
  getByCategoryId: async (categoryId) => {
    const [rows] = await pool.query(`
      SELECT 
        s.*,
        c.name as category_name,
        (SELECT COUNT(*) FROM products WHERE subcategory_id = s.id) as product_count
      FROM subcategories s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE s.category_id = ?
      ORDER BY s.created_at DESC
    `, [categoryId]);
    return rows;
  },

  // Create new subcategory (Simplified)
  create: async (subcategoryData) => {
    const { name, category_id } = subcategoryData;
    const [result] = await pool.query(
      "INSERT INTO subcategories (name, category_id) VALUES (?, ?)",
      [name, category_id]
    );
    return result.insertId;
  },

  // Update subcategory (Simplified)
  update: async (id, subcategoryData) => {
    const { name, category_id } = subcategoryData;
    await pool.query(
      "UPDATE subcategories SET name = ?, category_id = ? WHERE id = ?",
      [name, category_id, id]
    );
  },

  // Delete subcategory
  delete: async (id) => {
    await pool.query("DELETE FROM subcategories WHERE id = ?", [id]);
  },
};

module.exports = Subcategory;
