@echo off
echo 🚀 Vercel Deployment Script - Admin Application
echo ================================================

REM Check if Vercel CLI is installed
vercel --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Vercel CLI is not installed!
    echo [INFO] Installing Vercel CLI...
    call npm install -g vercel
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install Vercel CLI
        pause
        exit /b 1
    )
)

echo [INFO] Vercel CLI version:
vercel --version

echo.
echo [INFO] Starting Backend Deployment...
echo [INFO] Make sure you have your database credentials ready!
echo.

REM Deploy Backend
cd Pra
echo [INFO] Deploying Backend from: %CD%
vercel

if %errorlevel% neq 0 (
    echo [ERROR] Backend deployment failed!
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Backend deployed successfully!
echo [INFO] Note down the backend URL from above
echo.

REM Deploy Frontend
echo [INFO] Starting Frontend Deployment...
cd ..\admin-frontend
echo [INFO] Deploying Frontend from: %CD%

REM Ask for backend URL
set /p BACKEND_URL="Enter your backend URL (e.g., https://admin-backend-xxxxx.vercel.app): "

echo [INFO] Setting environment variable: VITE_API_URL=%BACKEND_URL%/api
vercel --env VITE_API_URL="%BACKEND_URL%/api"

if %errorlevel% neq 0 (
    echo [ERROR] Frontend deployment failed!
    pause
    exit /b 1
)

echo.
echo 🎉 Deployment completed successfully!
echo.
echo [INFO] Next steps:
echo 1. Go to Vercel dashboard
echo 2. Set environment variables for backend:
echo    - DB_HOST, DB_USER, DB_PASSWORD, DB_NAME
echo    - JWT_SECRET, COOKIE_SECRET
echo    - FRONTEND_URL (your frontend URL)
echo 3. Test your application
echo.
echo [INFO] For detailed instructions, see VERCEL_DEPLOYMENT.md
pause 