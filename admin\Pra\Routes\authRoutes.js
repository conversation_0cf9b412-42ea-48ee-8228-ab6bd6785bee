const express = require('express');
const authRoutes = express.Router();
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const { isAdmin } = require('../middleware/isadmin');
const userController = require('../controllers/userController');
const { assignRole } = require('../controllers/userRoleController');


// Public routes
authRoutes.post('/register', authController.register);
authRoutes.post('/login',authController.login);
authRoutes.get('/logout', authController.logout);
authRoutes.post('/validate-token', authController.validateToken);


// Protected routes
authRoutes.get('/me', auth, authController.getCurrentUser);

module.exports = authRoutes;