import React, { useState, useEffect } from 'react';
import { 
  FaShoppingCart, 
  FaHeart, 
  FaStar, 
  FaTruck, 
  FaShieldAlt, 
  FaUndo, 
  FaHeadset,
  FaFire,
  FaGift,
  FaPercent,
  FaArrowRight,
  FaPlay,
  FaPause,
  FaShoePrints,
  FaRocket,
  FaCrown,
  FaGem
} from 'react-icons/fa';
import { BsLightningCharge, BsBagHeart, BsArrowUpCircle } from 'react-icons/bs';
import { MdLocalOffer, MdTrendingUp, MdFlashOn } from 'react-icons/md';
import ProductCard from '../components/ui/ProductCard';
import { categoryService } from '../services/categoryService';
import { subcategoryService } from '../services/subcategoryService';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, SiAdidas, SiPuma, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ance } from 'react-icons/si';

const Home = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subcategories, setSubcategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentBrandIndex, setCurrentBrandIndex] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [visibleCount, setVisibleCount] = useState(8);
  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();

  // Debug useEffect to track state changes
  useEffect(() => {
    console.log('🔍 Current state values:', {
      products: products.length,
      categories: categories.length,
      subcategories: subcategories.length,
      loading,
      error
    });
  }, [products, categories, subcategories, loading, error]);

  useEffect(() => {
    fetchData();
  }, []);

  // Auto-slide brands
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBrandIndex((prev) => (prev + 1) % brands.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Fetching data...');
      
      // Fetch products, categories, and subcategories in parallel
      const [productsResponse, categoriesResponse, subcategoriesResponse] = await Promise.all([
        axios.get('http://localhost:5001/api/products'),
        categoryService.getAllCategories(),
        subcategoryService.getAllSubcategories()
      ]);

      console.log('Products response:', productsResponse.data);
      console.log('Categories response:', categoriesResponse);
      console.log('Subcategories response:', subcategoriesResponse);

      // Extract data properly from different response structures
      let productsData = [];
      let categoriesData = [];
      let subcategoriesData = [];

      // Handle products data
      if (productsResponse.data?.success && productsResponse.data?.data?.products) {
        productsData = productsResponse.data.data.products;
      } else if (productsResponse.data?.products) {
        productsData = productsResponse.data.products;
      } else if (Array.isArray(productsResponse.data)) {
        productsData = productsResponse.data;
      }

      // Handle categories data
      if (categoriesResponse?.data) {
        categoriesData = Array.isArray(categoriesResponse.data) ? categoriesResponse.data : [];
      } else if (Array.isArray(categoriesResponse)) {
        categoriesData = categoriesResponse;
      }

      // Handle subcategories data
      if (subcategoriesResponse?.data) {
        subcategoriesData = Array.isArray(subcategoriesResponse.data) ? subcategoriesResponse.data : [];
      } else if (Array.isArray(subcategoriesResponse)) {
        subcategoriesData = subcategoriesResponse;
      }

      console.log('📦 Extracted data:', {
        products: productsData.length,
        categories: categoriesData.length,
        subcategories: subcategoriesData.length
      });

      // Set state with proper validation
      setProducts(Array.isArray(productsData) ? productsData : []);
      setCategories(Array.isArray(categoriesData) ? categoriesData : []);
      setSubcategories(Array.isArray(subcategoriesData) ? subcategoriesData : []);

      console.log('✅ State updated successfully');
      
    } catch (error) {
      console.error('❌ Error fetching data:', error);
      setError('Failed to load data');
      // Set empty arrays as fallback
      setProducts([]);
      setCategories([]);
      setSubcategories([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryClick = async (categoryId) => {
    try {
      setSelectedCategory(categoryId);
      const response = await subcategoryService.getSubcategoriesByCategory(categoryId);
      setSubcategories(response.data || []);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    }
  };

  const handleSubcategoryClick = (subcategoryId) => {
    navigate(`/subcategory/${subcategoryId}`);
  };

  // Removed heavy animation variants for simpler UI

  const brands = [
    { name: 'Nike', icon: SiNike, color: 'text-blue-600' },
    { name: 'Adidas', icon: SiAdidas, color: 'text-black' },
    { name: 'Puma', icon: SiPuma, color: 'text-orange-500' },
    { name: 'Reebok', icon: SiReebok, color: 'text-red-600' },
    { name: 'Under Armour', icon: SiUnderarmour, color: 'text-gray-800' },
    { name: 'New Balance', icon: SiNewbalance, color: 'text-gray-600' }
  ];

  const features = [
    { icon: FaTruck, title: 'Free Shipping', desc: 'On orders above ₹499' },
    { icon: FaShieldAlt, title: 'Secure Payment', desc: '100% secure checkout' },
    { icon: FaUndo, title: 'Easy Returns', desc: '30 day return policy' },
    { icon: FaHeadset, title: '24/7 Support', desc: 'Dedicated support' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <div className="text-5xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-red-800 mb-2">Error Loading Data</h2>
          <p className="text-red-600 mb-6">{error}</p>
          <button onClick={fetchData} className="bg-red-600 text-white px-5 py-2 rounded-md hover:bg-red-700">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-white text-gray-800 py-16 border-b">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Welcome to Shop Hub</h1>
          <p className="text-base md:text-lg mb-6 text-gray-600 max-w-2xl mx-auto">
            Discover great products with simple, clean shopping experience.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button className="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700">
              <FaRocket className="inline mr-2" />
              Start Shopping
            </button>
            <button className="border border-gray-300 text-gray-700 px-6 py-3 rounded-md font-semibold hover:bg-gray-100">
              <FaGift className="inline mr-2" />
              View Offers
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800">Why Choose Shop Hub?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <div className="w-14 h-14 mx-auto mb-3 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl">
                  <feature.icon />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{feature.title}</h3>
                <p className="text-gray-600">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800">Shop by Category</h2>
          
          {/* Debug info */}
          <div className="text-center mb-4 text-sm text-gray-500">
            Categories loaded: {categories.length} | Products loaded: {products.length}
          </div>
          
          {categories.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">🛍️</div>
              <p className="text-gray-600">Loading categories...</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {categories.map((category, index) => (
                <div
                  key={category.id || index}
                  className="text-center cursor-pointer group"
                  onClick={() => handleCategoryClick(category.id)}
                >
                  <div className="w-20 h-20 mx-auto mb-3 bg-blue-600 rounded-2xl flex items-center justify-center text-white text-3xl shadow-sm">
                    {getCategoryIcon(category.name)}
                  </div>
                  <h3 className="font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
                    {category.name}
                  </h3>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-800">Featured Products</h2>
            <button className="flex items-center text-blue-600 font-semibold hover:text-blue-800">
              View All <FaArrowRight className="ml-2" />
            </button>
          </div>

          {/* Debug info */}
          <div className="text-center mb-4 text-sm text-gray-500">
            Products loaded: {products.length} | Visible: {visibleCount}
          </div>
          
          {products.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No products found</h3>
              <p className="text-gray-600">Loading products...</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {products.slice(0, visibleCount).map((product, index) => (
                  <div key={product.id || index}>
                    <ProductCard product={product} />
                  </div>
                ))}
              </div>
              
              {products.length > visibleCount && (
                <div className="text-center mt-8">
                  <button
                    onClick={() => setVisibleCount(prev => prev + 4)}
                    className="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700"
                  >
                    Load More Products
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Brands Section (simplified) */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">Trusted Brands</h2>
          <div className="flex justify-center items-center gap-10 flex-wrap">
            {brands.map((brand) => (
              <div key={brand.name} className="text-center">
                <brand.icon className={`text-5xl ${brand.color}`} />
                <p className="mt-2 text-gray-600">{brand.name}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Special Offers (simplified) */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Special Offers</h2>
          <p className="text-base mb-6 max-w-2xl mx-auto text-gray-700">
            Get up to 70% off on selected items. Limited time offer!
          </p>
          <button className="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700">
            Shop Now
          </button>
        </div>
      </section>
    </div>
  );
};

// Helper function to get category icons
const getCategoryIcon = (categoryName) => {
  const iconMap = {
    'Electronics': '📱',
    'Clothing': '👕',
    'Books': '📚',
    'Home & Kitchen': '🔌',
    'Sports & Fitness': '🏋️',
    'Kids': '🧒'
  };
  return iconMap[categoryName] || '🛍️';
};

export default Home;
