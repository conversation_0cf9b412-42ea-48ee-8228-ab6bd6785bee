{"name": "pra", "version": "1.0.0", "description": "Authentication API", "main": "index.js", "scripts": {"start": "node production.js", "dev": "nodemon index.js", "prod": "node production.js", "vercel-build": "echo \"Vercel build completed\"", "build": "echo \"No build step necessary for backend\"", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.0", "nodemon": "^3.1.10"}}