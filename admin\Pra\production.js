require('dotenv').config();
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const path = require('path');
const authRoutes = require('./Routes/authRoutes');
const userRoutes = require('./Routes/userRoutes');
const { testConnection } = require('./Config/Db');
const roleRoutes = require('./Routes/roleRoutes');
const userRoleRoutes = require("./Routes/userRoleRoutes");
const categoryRoutes = require("./Routes/categoryRoutes");
const subcategoryRoutes = require("./Routes/subcategoryRoutes");
const productRoutes = require("./Routes/productRoutes");
const productDetailsRoutes = require("./Routes/productDetailsRoutes");
const orderRoutes = require("./Routes/orderRoutes');
const customerRoutes = require("./Routes/customerRoutes");

const app = express();

// Production CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'https://your-frontend-domain.com',
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser(process.env.COOKIE_SECRET || 'production_cookie_secret'));

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Security headers
app.disable('x-powered-by');
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// Test database connection
testConnection()
  .then(connected => {
    if (!connected) {
      console.warn('Warning: Database connection failed. Some features may not work properly.');
    }
  })
  .catch(err => {
    console.error('Error testing database connection:', err);
  });

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/roles', roleRoutes);
app.use("/api/user-roles", userRoleRoutes);  
app.use("/api/role-assign", userRoleRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/subcategories", subcategoryRoutes);
app.use("/api/products", productRoutes);
app.use("/api/product-details", productDetailsRoutes);
app.use("/api/orders", orderRoutes);
app.use("/api/admin/customers", customerRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 404 handler
app.use((req, res, next) => {
  res.status(404).json({ message: 'Route not found' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Admin API Server Running' });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Admin API Server running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'production'}`);
  console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'Not configured'}`);
}); 