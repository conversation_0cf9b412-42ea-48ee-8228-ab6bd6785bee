const express = require('express');
const router = express.Router();
const {
  createProductDetails,
  getProductDetails,
  updateProductDetails,
  deleteProductDetails,
  getAllProductDetails,
  upsertProductDetails
} = require('../controllers/productDetailsController');
const auth = require('../middleware/auth');
const { isAdmin } = require('../middleware/isadmin');

// Public: Get product details by product ID
router.get('/:productId', getProductDetails);
// Public: Get all product details with product info
router.get('/', getAllProductDetails);

// Protected: Create product details
router.post('/', auth, isAdmin, createProductDetails);
// Protected: Update product details
router.put('/:productId', auth, isAdmin, updateProductDetails);
// Protected: Delete product details
router.delete('/:productId', auth, isAdmin, deleteProductDetails);
// Protected: Upsert product details (create or update)
router.post('/upsert', auth, isAdmin, upsertProductDetails);

module.exports = router; 