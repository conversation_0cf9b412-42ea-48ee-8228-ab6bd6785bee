require('dotenv').config();
const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const path = require('path');
const authRoutes = require('./Routes/authRoutes');
const userRoutes = require('./Routes/userRoutes');
const { testConnection } = require('./Config/Db');
const roleRoutes = require('./Routes/roleRoutes');
const userRoleRoutes = require("./Routes/userRoleRoutes");
const categoryRoutes = require("./Routes/categoryRoutes");
const subcategoryRoutes = require("./Routes/subcategoryRoutes");
const productRoutes = require("./Routes/productRoutes");
const productDetailsRoutes = require("./Routes/productDetailsRoutes");
const orderRoutes = require("./Routes/orderRoutes");
const customerRoutes = require("./Routes/customerRoutes");


const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174'], // Allow both ports
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser(process.env.COOKIE_SECRET || 'cookie_secret_key'));

// Serve uploaded images statically
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Disable X-Powered-By header for security
app.disable('x-powered-by');

// Test database connection
testConnection()
  .then(connected => {
    if (!connected) {
      console.warn('Warning: Database connection failed. Some features may not work properly.');
    }
  })
  .catch(err => {
    console.error('Error testing database connection:', err);
  });

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/roles', roleRoutes);
app.use("/api/user-roles", userRoleRoutes);  
app.use("/api/role-assign", userRoleRoutes);

// Category routes
app.use("/api/categories", categoryRoutes);
// Subcategory routes
app.use("/api/subcategories", subcategoryRoutes);
// Product routes
app.use("/api/products", productRoutes);
// Product details routes
app.use("/api/product-details", productDetailsRoutes);
// Order routes
app.use("/api/orders", orderRoutes);
// Customer routes
app.use("/api/admin/customers", customerRoutes);


// 404 handler
app.use((req, res, next) => {
  res.status(404).json({ message: 'Route not found' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});


// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the API' });
});



// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Admin API Server running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
});
