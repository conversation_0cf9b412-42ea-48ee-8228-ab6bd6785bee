import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Swal from 'sweetalert2';
import { useAuth } from '../../context/AuthContext';
import cartService from '../../services/cartService';
import wishlistService from '../../services/wishlistService';
import { FaShoppingCart, FaHeart, FaStar, FaEye, FaFire, FaGift, FaCrown } from 'react-icons/fa';
import { BsLightningCharge, BsArrowUpCircle } from 'react-icons/bs';
import { MdLocalOffer, MdTrendingUp } from 'react-icons/md';
import { Link, useNavigate } from 'react-router-dom';

const ProductCard = ({ product }) => {
  const { isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [wishlistLoading, setWishlistLoading] = useState(false);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const navigate = useNavigate();

  const image = product.images && product.images.length > 0 ? product.images[0] : 'https://images.meesho.com/images/products/1/1.jpg';

  const showSuccessAlert = (title, message) => {
    Swal.fire({
      title,
      text: message,
      icon: 'success',
      confirmButtonColor: '#3b82f6',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const showErrorAlert = (title, message) => {
    Swal.fire({
      title,
      text: message,
      icon: 'error',
      confirmButtonColor: '#3b82f6'
    });
  };

  const showLoginAlert = () => {
    Swal.fire({
      title: 'Login Required',
      text: 'Please login to continue',
      icon: 'info',
      confirmButtonColor: '#3b82f6',
      confirmButtonText: 'Login Now'
    });
  };

  const handleAddToCart = async (e) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      showLoginAlert();
      return;
    }

    try {
      setLoading(true);
      await cartService.addToCart(product.id, 1);

      // Show success message with cart option
      Swal.fire({
        icon: 'success',
        title: 'Added to Cart! 🛒',
        text: 'Product has been added to your cart successfully',
        showCancelButton: true,
        confirmButtonText: 'View Cart',
        cancelButtonText: 'Continue Shopping',
        confirmButtonColor: '#3b82f6',
        cancelButtonColor: '#6b7280'
      }).then((result) => {
        if (result.isConfirmed) {
          navigate('/cart');
        }
      });
    } catch (error) {
      showErrorAlert('Error!', 'Failed to add product to cart');
      console.error('Add to cart error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToWishlist = async (e) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      showLoginAlert();
      return;
    }

    try {
      setWishlistLoading(true);
      await wishlistService.addToWishlist(product.id);
      setIsInWishlist(true);
      showSuccessAlert('Added to Wishlist! ❤️', 'Product has been added to your wishlist');
    } catch (error) {
      showErrorAlert('Error!', 'Failed to add product to wishlist');
      console.error('Add to wishlist error:', error);
    } finally {
      setWishlistLoading(false);
    }
  };

  const handleQuickView = (e) => {
    e.stopPropagation();
    Swal.fire({
      title: product.name,
      html: `
        <div class="text-left">
          <img src="${image}" alt="${product.name}" class="w-full h-48 object-cover rounded-lg mb-4" />
          <p class="text-lg font-bold text-blue-600 mb-2">₹${product.price}</p>
          <p class="text-gray-600 mb-2">${product.description || 'No description available'}</p>
          <p class="text-sm text-gray-500">Category: ${product.category_name || 'N/A'}</p>
          <p class="text-sm text-gray-500">Stock: ${product.quantity || 0} units</p>
        </div>
      `,
      width: '500px',
      confirmButtonColor: '#3b82f6',
      confirmButtonText: 'Add to Cart'
    }).then((result) => {
      if (result.isConfirmed) {
        handleAddToCart(e);
      }
    });
  };

  // Enhanced animation variants
  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    },
    hover: {
      y: -15,
      scale: 1.03,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  const imageVariants = {
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.1,
      rotate: 5,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    },
    tap: {
      scale: 0.9
    }
  };

  const badgeVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { 
      scale: 1, 
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  return (
    <Link to={`/product/${product.id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group cursor-pointer relative border border-gray-100 hover:border-blue-200"
      >
        {/* Product Image */}
        <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-blue-50">
          <motion.img
            src={image}
            alt={product.name}
            className="w-full h-56 object-cover"
            loading="lazy"
            variants={imageVariants}
            whileHover="hover"
          />
          
          {/* Gradient Overlay */}
          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"
              />
            )}
          </AnimatePresence>
          
          {/* Action Buttons */}
          <div className="absolute top-4 right-4 flex flex-col space-y-3">
            <motion.button
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              onClick={handleAddToWishlist}
              disabled={wishlistLoading}
              className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg backdrop-blur-sm ${
                isInWishlist 
                  ? 'bg-red-500 text-white' 
                  : 'bg-white/90 text-gray-600 hover:bg-red-50 hover:text-red-500'
              }`}
            >
              <FaHeart className={`text-sm ${isInWishlist ? 'fill-current' : ''}`} />
            </motion.button>
            
            <motion.button
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              onClick={handleAddToCart}
              disabled={loading || product.quantity === 0}
              className="w-10 h-10 rounded-full bg-white/90 text-gray-600 hover:bg-blue-50 hover:text-blue-500 flex items-center justify-center shadow-lg backdrop-blur-sm transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FaShoppingCart className="text-sm" />
            </motion.button>

            <motion.button
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              onClick={handleQuickView}
              className="w-10 h-10 rounded-full bg-white/90 text-gray-600 hover:bg-purple-50 hover:text-purple-500 flex items-center justify-center shadow-lg backdrop-blur-sm transition-all duration-300"
            >
              <FaEye className="text-sm" />
            </motion.button>
          </div>

          {/* Badges */}
          <AnimatePresence>
            {product.quantity === 0 && (
              <motion.div
                variants={badgeVariants}
                initial="initial"
                animate="animate"
                className="absolute top-4 left-4 bg-red-500 text-white text-xs px-3 py-1 rounded-full font-medium shadow-lg"
              >
                Sold Out
              </motion.div>
            )}
            
            {product.price < 500 && (
              <motion.div
                variants={badgeVariants}
                initial="initial"
                animate="animate"
                className="absolute bottom-4 left-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center gap-1 shadow-lg"
              >
                <BsLightningCharge className="text-yellow-300" />
                Deal
              </motion.div>
            )}

            {product.featured && (
              <motion.div
                variants={badgeVariants}
                initial="initial"
                animate="animate"
                className="absolute top-4 left-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center gap-1 shadow-lg"
              >
                <FaCrown className="text-yellow-300" />
                Featured
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Product Details */}
        <div className="p-6">
          <h3 
            className="font-bold text-gray-800 mb-3 line-clamp-2 min-h-[48px] hover:text-blue-600 transition-colors text-lg"
          >
            {product.name}
          </h3>
          
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <motion.div
                  key={star}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: star * 0.1 }}
                >
                  <FaStar className="text-yellow-400 text-sm" />
                </motion.div>
              ))}
              <span className="text-sm text-gray-500 ml-2">(4.5)</span>
            </div>
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
              {product.quantity || 0} left
            </span>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <motion.span 
                className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                whileHover={{ scale: 1.05 }}
              >
                ₹{product.price}
              </motion.span>
              {product.price > 1000 && (
                <motion.span 
                  className="text-lg text-gray-400 line-through"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  ₹{Math.round(product.price * 1.2)}
                </motion.span>
              )}
            </div>
            
            {product.category_name && (
              <motion.span 
                className="text-xs text-gray-600 bg-gradient-to-r from-blue-50 to-purple-50 px-3 py-1 rounded-full border border-blue-200"
                whileHover={{ scale: 1.05 }}
              >
                {product.category_name}
              </motion.span>
            )}
          </div>

          {/* Quick Actions */}
          <motion.div 
            className="flex space-x-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleAddToCart}
              disabled={loading || product.quantity === 0}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              <FaShoppingCart className="text-sm" />
              {loading ? 'Adding...' : 'Add to Cart'}
            </motion.button>
          </motion.div>

          {/* Loading States */}
          <AnimatePresence>
            {(loading || wishlistLoading) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center rounded-2xl"
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </Link>
  );
};

export default ProductCard; 