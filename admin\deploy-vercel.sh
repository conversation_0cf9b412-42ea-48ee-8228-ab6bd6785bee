#!/bin/bash

echo "🚀 Vercel Deployment Script - Admin Application"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_blue() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI is not installed!"
    print_status "Installing Vercel CLI..."
    npm install -g vercel
    
    if [ $? -ne 0 ]; then
        print_error "Failed to install Vercel CLI"
        exit 1
    fi
fi

print_status "Vercel CLI version: $(vercel --version)"

echo ""
print_blue "Starting Backend Deployment..."
print_warning "Make sure you have your database credentials ready!"
echo ""

# Deploy Backend
cd Pra
print_status "Deploying Backend from: $(pwd)"

vercel

if [ $? -ne 0 ]; then
    print_error "Backend deployment failed!"
    exit 1
fi

echo ""
print_status "Backend deployed successfully!"
print_warning "Note down the backend URL from above"
echo ""

# Deploy Frontend
print_blue "Starting Frontend Deployment..."
cd ../admin-frontend
print_status "Deploying Frontend from: $(pwd)"

# Ask for backend URL
echo ""
read -p "Enter your backend URL (e.g., https://admin-backend-xxxxx.vercel.app): " BACKEND_URL

if [ -z "$BACKEND_URL" ]; then
    print_error "Backend URL is required!"
    exit 1
fi

print_status "Setting environment variable: VITE_API_URL=$BACKEND_URL/api"
vercel --env VITE_API_URL="$BACKEND_URL/api"

if [ $? -ne 0 ]; then
    print_error "Frontend deployment failed!"
    exit 1
fi

echo ""
print_status "🎉 Deployment completed successfully!"
echo ""
print_blue "Next steps:"
echo "1. Go to Vercel dashboard"
echo "2. Set environment variables for backend:"
echo "   - DB_HOST, DB_USER, DB_PASSWORD, DB_NAME"
echo "   - JWT_SECRET, COOKIE_SECRET"
echo "   - FRONTEND_URL (your frontend URL)"
echo "3. Test your application"
echo ""
print_status "For detailed instructions, see VERCEL_DEPLOYMENT.md" 