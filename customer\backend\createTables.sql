-- Create database if not exists
CREATE DATABASE IF NOT EXISTS ecommerce9;
USE ecommerce9;

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subcategories table  
CREATE TABLE IF NOT EXISTS subcategories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    images JSON,
    quantity INT DEFAULT 0,
    category_id INT,
    subcategory_id INT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(15) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Cart table
CREATE TABLE IF NOT EXISTS cart (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cod', 'online') DEFAULT 'cod',
    payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    shipping_address JSON NOT NULL,
    billing_address JSON,
    notes TEXT,
    status ENUM('pending', 'confirmed', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Sample data
INSERT INTO categories (name, description) VALUES 
('Electronics', 'Electronic gadgets and devices'),
('Clothing', 'Fashion and apparel'),
('Books', 'Books and publications'),
('Home & Kitchen', 'Home and kitchen products'),
('Sports & Fitness', 'Sports and fitness equipment'),
('Kids', 'Kids products and toys');

INSERT INTO subcategories (name, category_id) VALUES 
('Smartphones', 1),
('Laptops', 1),
('T-Shirts', 2),
('Jeans', 2),
('Fiction', 3),
('Non-Fiction', 3),
('Cookware', 4),
('Furniture', 4),
('Gym Equipment', 5),
('Outdoor Sports', 5),
('Toys', 6),
('Baby Care', 6);

INSERT INTO products (name, description, price, images, quantity, category_id, subcategory_id, featured) VALUES 
('iPhone 15', 'Latest iPhone model with advanced features', 99999.00, '["iphone1.jpg", "iphone2.jpg"]', 50, 1, 1, 1),
('MacBook Pro', 'Professional laptop for developers', 149999.00, '["macbook1.jpg"]', 25, 1, 2, 1),
('Cotton T-Shirt', 'Comfortable cotton t-shirt', 599.00, '["tshirt1.jpg"]', 100, 2, 3, 0),
('Denim Jeans', 'Stylish denim jeans', 1299.00, '["jeans1.jpg"]', 75, 2, 4, 0),
('Harry Potter Book', 'Magical adventure book', 299.00, '["book1.jpg"]', 200, 3, 5, 1),
('Non-Stick Pan', 'Quality non-stick cooking pan', 899.00, '["pan1.jpg"]', 60, 4, 7, 0),
('Dumbbells Set', 'Professional gym dumbbells', 2499.00, '["dumbbells1.jpg"]', 30, 5, 9, 1),
('Kids Toy Car', 'Fun toy car for children', 399.00, '["toy1.jpg"]', 150, 6, 11, 0);

-- Create indexes for better performance
CREATE INDEX idx_cart_customer ON cart(customer_id);
CREATE INDEX idx_cart_product ON cart(product_id);
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_number ON orders(order_number);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_featured ON products(featured);

