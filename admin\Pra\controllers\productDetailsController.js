const ProductDetailsModel = require('../model/productDetailsModel');

exports.createProductDetails = async (req, res) => {
  try {
    const result = await ProductDetailsModel.create(req.body);
    res.status(201).json({
      success: true,
      message: 'Product details created successfully',
      data: result
    });
  } catch (error) {
    console.error('Error creating product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating product details'
    });
  }
};

exports.getProductDetails = async (req, res) => {
  try {
    const productDetails = await ProductDetailsModel.getByProductId(req.params.productId);
    if (!productDetails) {
      return res.status(404).json({
        success: false,
        message: 'Product details not found'
      });
    }
    res.json({
      success: true,
      data: productDetails
    });
  } catch (error) {
    console.error('Error fetching product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching product details'
    });
  }
};

exports.updateProductDetails = async (req, res) => {
  try {
    await ProductDetailsModel.update(req.params.productId, req.body);
    res.json({
      success: true,
      message: 'Product details updated successfully'
    });
  } catch (error) {
    console.error('Error updating product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating product details'
    });
  }
};

exports.deleteProductDetails = async (req, res) => {
  try {
    await ProductDetailsModel.delete(req.params.productId);
    res.json({
      success: true,
      message: 'Product details deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting product details'
    });
  }
};

exports.getAllProductDetails = async (req, res) => {
  try {
    const productDetails = await ProductDetailsModel.getAllWithProductInfo();
    res.json({
      success: true,
      data: productDetails
    });
  } catch (error) {
    console.error('Error fetching all product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching product details'
    });
  }
};

exports.upsertProductDetails = async (req, res) => {
  try {
    const result = await ProductDetailsModel.upsert(req.body);
    res.json({
      success: true,
      message: 'Product details saved successfully',
      data: result
    });
  } catch (error) {
    console.error('Error upserting product details:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving product details'
    });
  }
};