@echo off
echo 🚀 Starting Admin Application Deployment...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo [INFO] Node.js version: 
node --version

REM Deploy Backend
echo.
echo [INFO] Deploying Backend...
cd Pra

if not exist "package.json" (
    echo [ERROR] package.json not found in backend directory
    pause
    exit /b 1
)

echo [INFO] Installing backend dependencies...
call npm install

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)

echo [INFO] Backend dependencies installed successfully

REM Check if .env file exists
if not exist ".env" (
    echo [WARNING] .env file not found. Please create one with your production configuration.
    echo [INFO] Creating sample .env file...
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_USER=root
        echo DB_PASSWORD=
        echo DB_NAME=your_database_name
        echo DB_PORT=3306
        echo.
        echo # Server Configuration
        echo PORT=3000
        echo NODE_ENV=production
        echo.
        echo # JWT Configuration
        echo JWT_SECRET=your_jwt_secret_key_here
        echo JWT_EXPIRES_IN=7d
        echo.
        echo # Cookie Configuration
        echo COOKIE_SECRET=your_cookie_secret_key_here
        echo.
        echo # CORS Configuration
        echo FRONTEND_URL=https://your-frontend-domain.com
    ) > .env
    echo [WARNING] Please edit .env file with your actual configuration before starting the server
)

echo [INFO] Backend ready for deployment
echo [INFO] To start backend server: npm run prod

REM Deploy Frontend
echo.
echo [INFO] Deploying Frontend...
cd ..\admin-frontend

if not exist "package.json" (
    echo [ERROR] package.json not found in frontend directory
    pause
    exit /b 1
)

echo [INFO] Installing frontend dependencies...
call npm install

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)

echo [INFO] Frontend dependencies installed successfully

echo [INFO] Building frontend for production...
call npm run build:prod

if %errorlevel% neq 0 (
    echo [ERROR] Failed to build frontend
    pause
    exit /b 1
)

echo [INFO] Frontend built successfully!

REM Check if dist folder exists
if exist "dist" (
    echo [INFO] Frontend build output available in dist/ folder
    echo [INFO] You can serve it using: npx serve -s dist -l 3001
) else (
    echo [ERROR] dist folder not found after build
    pause
    exit /b 1
)

echo.
echo 🎉 Deployment completed successfully!
echo.
echo [INFO] Next steps:
echo 1. Configure your .env file in backend with production values
echo 2. Start backend server: cd Pra ^&^& npm run prod
echo 3. Serve frontend: cd admin-frontend ^&^& npx serve -s dist -l 3001
echo 4. Or deploy to your preferred hosting platform
echo.
echo [INFO] For detailed instructions, see DEPLOYMENT.md
pause 