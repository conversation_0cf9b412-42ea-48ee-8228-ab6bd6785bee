{"name": "admin-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "vercel-build": "vite build", "preview": "vite preview", "lint": "eslint .", "deploy": "npm run build:prod"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.80.7", "axios": "^1.9.0", "clsx": "^2.1.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.516.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^6.30.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.3", "vite": "^6.3.5"}}