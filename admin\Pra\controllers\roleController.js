const roleModel = require("../model/roleModel");

const createRole = async (req, res) => {
  const { role_name } = req.body;
  if (!role_name) return res.status(400).json({ success: false, message: "Role name is required." });

  try {
    await roleModel.createRole(role_name);
    res.status(201).json({ success: true, message: "Role created successfully." });
  } catch (err) {
    res.status(err.code === 'ER_DUP_ENTRY' ? 409 : 500).json({
      success: false,
      message: err.code === 'ER_DUP_ENTRY' ? "Role already exists." : "Server error."
    });
  }
};

const getRoles = async (req, res) => {
  try {
    const [roles] = await roleModel.getAllRoles();
    res.json({ success: true, data: roles });
  } catch (err) {
    res.status(500).json({ success: false, message: "Failed to fetch roles." });
  }
};

const updateRole = async (req, res) => {
  const { role_name } = req.body;
  if (!role_name) return res.status(400).json({ success: false, message: "Role name is required." });

  try {
    await roleModel.updateRole(req.params.id, role_name);
    res.json({ success: true, message: "Role updated successfully." });
  } catch (err) {
    res.status(500).json({ success: false, message: "Update failed." });
  }
};

const deleteRole = async (req, res) => {
  try {
    await roleModel.deleteRole(req.params.id);
    res.json({ success: true, message: "Role deleted successfully." });
  } catch (err) {
    res.status(500).json({ success: false, message: "Delete failed." });
  }
};

module.exports = { createRole, getRoles, updateRole, deleteRole };
