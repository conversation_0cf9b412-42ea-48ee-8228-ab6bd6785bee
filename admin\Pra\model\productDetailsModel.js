// model/productDetailsModel.js
const db = require('../config/db'); // <-- your database connection file ka path
// ensure this file exports a MySQL connection or pool

const ProductDetailsModel = {
  // CREATE product
  createProduct: (data, callback) => {
    const query = `INSERT INTO product_details (name, price, description, category, created_at) VALUES (?, ?, ?, ?, NOW())`;
    const values = [data.name, data.price, data.description, data.category];
    db.query(query, values, callback);
  },

  // READ all products
  getAllProducts: (callback) => {
    db.query(`SELECT * FROM product_details`, callback);
  },

  // READ single product
  getProductById: (id, callback) => {
    db.query(`SELECT * FROM product_details WHERE id = ?`, [id], callback);
  },

  // UPDATE product
  updateProduct: (id, data, callback) => {
    const query = `UPDATE product_details SET name = ?, price = ?, description = ?, category = ? WHERE id = ?`;
    const values = [data.name, data.price, data.description, data.category, id];
    db.query(query, values, callback);
  },

  // DELETE product
  deleteProduct: (id, callback) => {
    db.query(`DELETE FROM product_details WHERE id = ?`, [id], callback);
  },
};

module.exports = ProductDetailsModel;
