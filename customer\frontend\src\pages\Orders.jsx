import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaShoppingBag, 
  FaEye, 
  FaTimes, 
  FaArrowLeft,
  FaCalendarAlt,
  FaRupeeSign,
  FaBox,
  FaTruck,
  FaCheckCircle,
  FaTimesCircle,
  FaClock
} from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import orderService from '../services/orderService';
import Swal from 'sweetalert2';

const Orders = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
    fetchOrders();
  }, [user, navigate, currentPage, statusFilter]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      console.log('🔍 Fetching orders for user:', user.id);
      
      const response = await orderService.getOrders(currentPage, 10);
      console.log('📦 Orders response:', response);

      if (response.success) {
        const ordersData = response.data.orders || [];
        console.log('📦 Orders data:', ordersData);
        
        setOrders(ordersData);
        setTotalPages(response.data.pagination?.totalPages || 1);
      } else {
        console.log('❌ Orders response not successful:', response);
        setOrders([]);
      }
    } catch (error) {
      console.error('❌ Error fetching orders:', error);

      // Check if it's an authentication error
      if (error.status === 401 || error.message?.includes('token') || error.message?.includes('auth')) {
        Swal.fire({
          icon: 'warning',
          title: 'Session Expired',
          text: 'Please login again to view your orders',
          confirmButtonText: 'Login'
        }).then(() => {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('user');
          navigate('/login');
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load orders. Please try again.'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = async (orderId, orderNumber) => {
    const result = await Swal.fire({
      icon: 'warning',
      title: 'Cancel Order',
      text: `Are you sure you want to cancel order #${orderNumber}?`,
      showCancelButton: true,
      confirmButtonText: 'Yes, Cancel',
      cancelButtonText: 'No, Keep Order',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6'
    });

    if (result.isConfirmed) {
      try {
        await orderService.updateOrderStatus(orderId, 'cancelled', 'Order cancelled by customer');
        Swal.fire('Cancelled!', 'Your order has been cancelled.', 'success');
        fetchOrders(); // Refresh orders
      } catch (error) {
        console.error('Error cancelling order:', error);
        Swal.fire('Error!', 'Failed to cancel order. Please try again.', 'error');
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-indigo-100 text-indigo-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return <FaClock className="w-4 h-4" />;
      case 'confirmed': return <FaCheckCircle className="w-4 h-4" />;
      case 'processing': return <FaBox className="w-4 h-4" />;
      case 'shipped': return <FaTruck className="w-4 h-4" />;
      case 'delivered': return <FaCheckCircle className="w-4 h-4" />;
      case 'cancelled': return <FaTimesCircle className="w-4 h-4" />;
      default: return <FaBox className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <motion.div 
        className="flex items-center justify-center min-h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="min-h-screen bg-gray-50 py-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div 
          className="mb-8"
          initial={{ y: -20 }}
          animate={{ y: 0 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
              >
                <FaArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
                <p className="text-gray-600">Track your order status and history</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Orders</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Orders List */}
        {orders.length === 0 ? (
          <motion.div 
            className="text-center py-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <FaShoppingBag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-6">You haven't placed any orders yet.</p>
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Start Shopping
            </Link>
          </motion.div>
        ) : (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {orders.map((order, index) => (
              <motion.div
                key={order.id}
                className="bg-white rounded-lg shadow-md overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -2 }}
              >
                {/* Order Header */}
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-blue-100 rounded-full">
                        <FaShoppingBag className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          Order #{order.order_number}
                        </h3>
                        <p className="text-sm text-gray-500">
                          <FaCalendarAlt className="inline w-3 h-3 mr-1" />
                          {formatDate(order.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                        <span className="flex items-center space-x-1">
                          {getStatusIcon(order.status)}
                          <span className="capitalize">{order.status}</span>
                        </span>
                      </span>
                      {order.status === 'pending' && (
                        <button
                          onClick={() => handleCancelOrder(order.id, order.order_number)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
                          title="Cancel Order"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Order Details */}
                <div className="px-6 py-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <FaRupeeSign className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        Total: <span className="font-semibold text-gray-900">₹{order.total_amount}</span>
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaBox className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        Items: <span className="font-semibold text-gray-900">{order.item_count || 1}</span>
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FaTruck className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        Payment: <span className="font-semibold text-gray-900 capitalize">{order.payment_status}</span>
                      </span>
                    </div>
                  </div>

                  {/* Products */}
                  {order.products && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Products:</h4>
                      <p className="text-sm text-gray-600">{order.products}</p>
                    </div>
                  )}

                  {/* Shipping Address */}
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Shipping Address:</h4>
                    <p className="text-sm text-gray-600">{order.shipping_address}</p>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-6 flex items-center justify-between">
                    <button
                      onClick={() => navigate(`/orders/${order.id}`)}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                    >
                      <FaEye className="w-4 h-4 mr-2" />
                      View Details
                    </button>
                    
                    {order.status === 'delivered' && (
                      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <FaCheckCircle className="w-4 h-4 mr-2" />
                        Rate & Review
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div 
            className="mt-8 flex items-center justify-center space-x-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="px-4 py-2 text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default Orders;
