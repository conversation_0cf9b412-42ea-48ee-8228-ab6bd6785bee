# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=12345
DB_NAME=ecommerce9
DB_PORT=3306

# Server Configuration
PORT=5001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=customer_super_secret_jwt_key_2024_ecommerce_platform_secure
JWT_REFRESH_SECRET=customer_refresh_secret_jwt_key_2024_ecommerce_platform_secure
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Twilio Configuration (for OTP)
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=762225d7bba198348b7ad420694fe9de
TWILIO_PHONE_NUMBER=+***********

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=sgjq pglg tngx psll

# Frontend URL (for CORS)
FRONTEND_URL=https://ecommerce9-w1bw.vercel.app

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=customer_session_secret_2024_ecommerce_platform

# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_cvqgtKzWYqo3hB
RAZORPAY_KEY_SECRET=wob0FLGPZM2nIz66kOnkY6VM
