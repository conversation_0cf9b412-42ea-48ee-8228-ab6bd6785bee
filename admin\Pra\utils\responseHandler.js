const sendResponse = (res, code, success, message, data = null) => {
  const response = {
    success,
    message,
    ...(data && { data })
  };
  return res.status(code).json(response);
};

module.exports = {
  sendResponse,
  sendSuccess: (res, code, data, message = "Success") =>
    res.status(code).json({ success: true, message, data }),
  sendError: (res, code, message = "Something went wrong", error = null) =>
    res.status(code).json({ success: false, message, error }),
};