const express = require("express");
const userRoleRouter = express.Router();
const userRoleController = require("../controllers/userRoleController");
const auth = require("../middleware/auth");
const { isAdmin } = require("../middleware/isadmin");

// Apply auth middleware to all routes
userRoleRouter.use(auth);

// Role assignment routes
userRoleRouter.post("/assign", isAdmin, userRoleController.assignRole);
userRoleRouter.put("/update", isAdmin, userRoleController.updateRole);
userRoleRouter.delete("/:userid/:roleid", isAdmin, userRoleController.removeRole);

// Role-based fetch routes
userRoleRouter.get("/user/:userid", isAdmin, userRoleController.getUserRoles);
userRoleRouter.get("/role/:roleid", isAdmin, userRoleController.getUsersByRole);

// ✅ New route for GET all assignments
userRoleRouter.get("/", isAdmin, userRoleController.getAllAssignments);

module.exports = userRoleRouter;
