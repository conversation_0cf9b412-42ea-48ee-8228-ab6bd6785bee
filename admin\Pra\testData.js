const { pool } = require("./Config/Db");

const addTestData = async () => {
  try {
    console.log('🔄 Adding test data to database...');

    // Add categories
    const categories = [
      { name: 'Women Ethnic' },
      { name: 'Women Western' },
      { name: 'Men' },
      { name: 'Kids' },
      { name: 'Home & Kitchen' },
      { name: 'Beauty & Health' },
      { name: 'Jewellery' },
      { name: 'Electronics' }
    ];

    console.log('📂 Adding categories...');
    for (const category of categories) {
      await pool.query("INSERT INTO categories (name) VALUES (?)", [category.name]);
      console.log(`✅ Added category: ${category.name}`);
    }

    // Add subcategories
    const subcategories = [
      { name: 'Saree<PERSON>', category_id: 1 },
      { name: '<PERSON><PERSON>', category_id: 1 },
      { name: '<PERSON><PERSON><PERSON>', category_id: 2 },
      { name: 'Tops', category_id: 2 },
      { name: 'Shirts', category_id: 3 },
      { name: 'Pan<PERSON>', category_id: 3 },
      { name: 'Baby Clothes', category_id: 4 },
      { name: 'Kids Toys', category_id: 4 },
      { name: 'Kitchen Appliances', category_id: 5 },
      { name: 'Home Decor', category_id: 5 },
      { name: 'Makeup', category_id: 6 },
      { name: 'Skincare', category_id: 6 },
      { name: 'Necklaces', category_id: 7 },
      { name: 'Earrings', category_id: 7 },
      { name: 'Mobiles', category_id: 8 },
      { name: 'Laptops', category_id: 8 }
    ];

    console.log('📋 Adding subcategories...');
    for (const subcategory of subcategories) {
      await pool.query("INSERT INTO subcategories (name, category_id) VALUES (?, ?)", 
        [subcategory.name, subcategory.category_id]);
      console.log(`✅ Added subcategory: ${subcategory.name}`);
    }

    // Add products
    const products = [
      {
        name: 'Beautiful Silk Saree',
        description: 'Elegant silk saree perfect for special occasions',
        price: 1299,
        category_id: 1,
        subcategory_id: 1,
        image_url: 'https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Saree',
        stock_quantity: 50
      },
      {
        name: 'Designer Kurti',
        description: 'Stylish kurti with modern design',
        price: 899,
        category_id: 1,
        subcategory_id: 2,
        image_url: 'https://via.placeholder.com/300x400/4ECDC4/FFFFFF?text=Kurti',
        stock_quantity: 30
      },
      {
        name: 'Summer Dress',
        description: 'Comfortable summer dress for casual wear',
        price: 599,
        category_id: 2,
        subcategory_id: 3,
        image_url: 'https://via.placeholder.com/300x400/45B7D1/FFFFFF?text=Dress',
        stock_quantity: 25
      },
      {
        name: 'Men\'s Formal Shirt',
        description: 'Professional formal shirt for office wear',
        price: 799,
        category_id: 3,
        subcategory_id: 5,
        image_url: 'https://via.placeholder.com/300x400/96CEB4/FFFFFF?text=Shirt',
        stock_quantity: 40
      },
      {
        name: 'Baby Romper',
        description: 'Comfortable romper for babies',
        price: 299,
        category_id: 4,
        subcategory_id: 7,
        image_url: 'https://via.placeholder.com/300x400/FFEAA7/FFFFFF?text=Baby',
        stock_quantity: 20
      },
      {
        name: 'Kitchen Mixer',
        description: 'High-quality kitchen mixer for daily use',
        price: 2499,
        category_id: 5,
        subcategory_id: 9,
        image_url: 'https://via.placeholder.com/300x400/DDA0DD/FFFFFF?text=Mixer',
        stock_quantity: 15
      },
      {
        name: 'Lipstick Set',
        description: 'Beautiful lipstick set with multiple shades',
        price: 399,
        category_id: 6,
        subcategory_id: 11,
        image_url: 'https://via.placeholder.com/300x400/FFB6C1/FFFFFF?text=Lipstick',
        stock_quantity: 35
      },
      {
        name: 'Gold Necklace',
        description: 'Elegant gold necklace for special occasions',
        price: 4999,
        category_id: 7,
        subcategory_id: 13,
        image_url: 'https://via.placeholder.com/300x400/FFD700/FFFFFF?text=Necklace',
        stock_quantity: 10
      }
    ];

    console.log('🛍️ Adding products...');
    for (const product of products) {
      await pool.query(
        "INSERT INTO products (name, description, price, category_id, subcategory_id, image_url, stock_quantity) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [product.name, product.description, product.price, product.category_id, product.subcategory_id, product.image_url, product.stock_quantity]
      );
      console.log(`✅ Added product: ${product.name}`);
    }

    console.log('🎉 Test data added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding test data:', error);
    process.exit(1);
  }
};

addTestData(); 