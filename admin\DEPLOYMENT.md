# Admin Application Deployment Guide

## 🚀 Backend Deployment (Node.js/Express)

### Prerequisites
- Node.js 18+ installed
- MySQL database running
- Environment variables configured

### Steps to Deploy Backend

1. **Install Dependencies**
   ```bash
   cd admin/Pra
   npm install
   ```

2. **Configure Environment Variables**
   Create `.env` file with:
   ```env
   DB_HOST=your_database_host
   DB_USER=your_database_user
   DB_PASSWORD=your_database_password
   DB_NAME=your_database_name
   DB_PORT=3306
   PORT=3000
   NODE_ENV=production
   JWT_SECRET=your_jwt_secret_key
   COOKIE_SECRET=your_cookie_secret
   FRONTEND_URL=https://your-frontend-domain.com
   ```

3. **Start Production Server**
   ```bash
   npm run prod
   # or
   node production.js
   ```

4. **For PM2 (Process Manager)**
   ```bash
   npm install -g pm2
   pm2 start production.js --name "admin-backend"
   pm2 startup
   pm2 save
   ```

## 🌐 Frontend Deployment (React/Vite)

### Prerequisites
- Node.js 18+ installed
- Backend API running

### Steps to Deploy Frontend

1. **Install Dependencies**
   ```bash
   cd admin/admin-frontend
   npm install
   ```

2. **Build for Production**
   ```bash
   npm run build:prod
   # or
   npm run deploy
   ```

3. **Configure API URL**
   Set environment variable:
   ```bash
   export VITE_API_URL=https://your-backend-domain.com/api
   ```

4. **Serve Built Files**
   ```bash
   # Using serve package
   npm install -g serve
   serve -s dist -l 3001
   
   # Or using nginx/apache
   # Copy dist folder contents to web server directory
   ```

## 🔧 Production Configuration

### Backend Security
- CORS configured for production domain
- Security headers enabled
- Environment-based configuration
- Health check endpoint

### Frontend Optimization
- Code splitting enabled
- Bundle optimization
- Production build configuration
- Environment-based API configuration

## 📱 Deployment Platforms

### Backend Options
- **Vercel**: Serverless deployment
- **Railway**: Easy Node.js hosting
- **Heroku**: Traditional hosting
- **DigitalOcean**: VPS hosting
- **AWS EC2**: Cloud hosting

### Frontend Options
- **Vercel**: React hosting
- **Netlify**: Static site hosting
- **GitHub Pages**: Free hosting
- **Firebase Hosting**: Google hosting
- **AWS S3**: Static hosting

## 🚨 Important Notes

1. **Database**: Ensure MySQL is accessible from production server
2. **Environment Variables**: Never commit sensitive data
3. **CORS**: Configure allowed origins properly
4. **SSL**: Use HTTPS in production
5. **Monitoring**: Set up logging and monitoring
6. **Backup**: Regular database backups

## 🔍 Troubleshooting

### Common Issues
- CORS errors: Check FRONTEND_URL configuration
- Database connection: Verify database credentials
- Build errors: Check Node.js version compatibility
- Port conflicts: Ensure ports are available

### Health Checks
- Backend: `GET /health`
- Frontend: Check build output in dist folder 