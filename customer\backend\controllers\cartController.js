const cartModel = require('../models/cartModel');
const sendResponse = require('../utils/sendResponse');

const cartController = {
  // Add item to cart
  addToCart: async (req, res) => {
    try {
      const { productId, quantity = 1 } = req.body;
      const customerId = req.customer.id;

      if (!productId) {
        return sendResponse(res, 400, false, "Product ID is required");
      }

      const result = await cartModel.addToCart(customerId, productId, Number(quantity));
      if (result) {
        sendResponse(res, 200, true, "Item added to cart successfully");
      } else {
        sendResponse(res, 500, false, "Failed to add item to cart");
      }
    } catch (error) {
      console.error("Add to cart controller error:", error);
      const status = error.statusCode || 500;
      const message = error.message || "Failed to add item to cart";
      sendResponse(res, status, false, message);
    }
  },

  // Get cart items
  getCart: async (req, res) => {
    try {
      const customerId = req.customer.id;
      const cartItems = await cartModel.getCart(customerId);
      
      const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      sendResponse(res, 200, true, "Cart retrieved successfully", {
        items: cartItems,
        totalItems,
        totalPrice
      });
    } catch (error) {
      console.error("Get cart controller error:", error);
      sendResponse(res, 500, false, "Failed to get cart");
    }
  },

  // Remove item from cart
  removeFromCart: async (req, res) => {
    try {
      const { productId } = req.params;
      const customerId = req.customer.id;

      const success = await cartModel.removeFromCart(customerId, productId);
      
      if (success) {
        sendResponse(res, 200, true, "Item removed from cart");
      } else {
        sendResponse(res, 404, false, "Item not found in cart");
      }
    } catch (error) {
      console.error("Remove from cart controller error:", error);
      sendResponse(res, 500, false, "Failed to remove item from cart");
    }
  },

  // Update cart item quantity
  updateQuantity: async (req, res) => {
    try {
      const { productId } = req.params;
      const { quantity } = req.body;
      const customerId = req.customer.id;

      if (quantity === undefined || quantity < 0) {
        return sendResponse(res, 400, false, "Valid quantity is required");
      }

      const success = await cartModel.updateQuantity(customerId, productId, quantity);
      
      if (success) {
        sendResponse(res, 200, true, "Cart updated successfully");
      } else {
        sendResponse(res, 404, false, "Item not found in cart");
      }
    } catch (error) {
      console.error("Update cart quantity controller error:", error);
      sendResponse(res, 500, false, "Failed to update cart");
    }
  },

  // Clear cart
  clearCart: async (req, res) => {
    try {
      const customerId = req.customer.id;
      const success = await cartModel.clearCart(customerId);
      if (success) {
        sendResponse(res, 200, true, "Cart cleared successfully");
      } else {
        sendResponse(res, 500, false, "Failed to clear cart");
      }
    } catch (error) {
      console.error("Clear cart controller error:", error);
      sendResponse(res, 500, false, "Failed to clear cart");
    }
  }
};

module.exports = cartController; 