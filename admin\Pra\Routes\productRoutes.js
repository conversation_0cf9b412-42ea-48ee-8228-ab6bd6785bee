const express = require("express");
const router = express.Router();
const auth = require("../middleware/auth");
const {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  getProductsByCategory,
  getProductsBySubcategory
} = require("../controllers/productController");

// Middleware for authentication (only for write operations)
// router.use(auth);

// Create product (Manager-only)
router.post("/", auth, createProduct);

// Get all products (public)
router.get("/", getAllProducts);

// Get products by category (public)
router.get("/category/:categoryId", getProductsByCategory);

// Get products by subcategory (public)
router.get("/subcategory/:subcategoryId", getProductsBySubcategory);

// Get product by ID (public)
router.get("/:id", getProductById);

// Update product
router.put("/:id", auth, updateProduct);

// Delete product
router.delete("/:id", auth, deleteProduct);

module.exports = router;
