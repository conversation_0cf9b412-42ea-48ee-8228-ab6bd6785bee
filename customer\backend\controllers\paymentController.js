const Razorpay = require('razorpay');
const { pool } = require('../config/db');

// Initialize Razorpay client conditionally
let razorpay;
if (process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET) {
  razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET,
  });
  console.log('✅ Razorpay client initialized successfully');
} else {
  console.log('❌ Razorpay keys missing in environment');
}

// Create Razorpay order
const createOrder = async (req, res) => {
  try {
    const { amount, currency = 'INR', receipt, order_id } = req.body;

    // Use order_id as receipt if receipt not provided (convert to string)
    const finalReceipt = receipt || (order_id ? String(order_id) : `receipt_${Date.now()}`);

    if (!amount) {
      return res.status(400).json({
        success: false,
        message: 'Amount is required'
      });
    }

    if (!razorpay) {
      return res.status(500).json({
        success: false,
        message: 'Payment service not configured'
      });
    }

    const options = {
      amount: Math.round(amount * 100), // Convert to paise
      currency,
      receipt: finalReceipt,
      notes: {
        source: 'customer_module',
        order_id: order_id || 'unknown'
      }
    };

    const order = await razorpay.orders.create(options);

    // Save payment record to database
    try {
      const [result] = await pool.execute(
        'INSERT INTO order_payments (payment_id, amount, currency, payment_method, status, razorpay_order_id) VALUES (?, ?, ?, ?, ?, ?)',
        [receipt, amount, currency, 'razorpay', 'pending', order.id]
      );
      console.log('✅ Payment record saved to database:', result.insertId);
    } catch (dbError) {
      console.error('❌ Database error saving payment:', dbError);
      // Continue with payment even if database save fails
    }

    res.status(200).json({
      success: true,
      message: 'Order created successfully',
      data: {
        orderId: order.id,
        amount: order.amount,
        currency: order.currency,
        receipt: order.receipt
      }
    });

  } catch (error) {
    console.error('❌ Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: error.message
    });
  }
};

// Verify payment
const verifyPayment = async (req, res) => {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return res.status(400).json({
        success: false,
        message: 'Missing payment verification parameters'
      });
    }

    // Verify signature
    const text = `${razorpay_order_id}|${razorpay_payment_id}`;
    const crypto = require('crypto');
    const signature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(text)
      .digest('hex');

    if (signature !== razorpay_signature) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment signature'
      });
    }

    // Update payment status in database
    try {
      const [result] = await pool.execute(
        'UPDATE order_payments SET status = ?, razorpay_payment_id = ?, updated_at = NOW() WHERE razorpay_order_id = ?',
        ['completed', razorpay_payment_id, razorpay_order_id]
      );
      console.log('✅ Payment status updated in database:', result.affectedRows);
    } catch (dbError) {
      console.error('❌ Database error updating payment:', dbError);
      // Continue with verification even if database update fails
    }

    res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        orderId: razorpay_order_id,
        paymentId: razorpay_payment_id
      }
    });

  } catch (error) {
    console.error('❌ Verify payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify payment',
      error: error.message
    });
  }
};

// Get payment status
const getPaymentDetails = async (req, res) => {
  try {
    const { order_id } = req.params;

    const [rows] = await pool.execute(
      'SELECT * FROM order_payments WHERE razorpay_order_id = ?',
      [order_id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Payment status retrieved successfully',
      data: rows[0]
    });

  } catch (error) {
    console.error('❌ Get payment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment status',
      error: error.message
    });
  }
};

module.exports = {
  createOrder,
  verifyPayment,
  getPaymentDetails
}; 
