#!/bin/bash

echo "🚀 Starting Admin Application Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version: $(node -v)"

# Deploy Backend
echo ""
print_status "Deploying Backend..."
cd Pra

if [ ! -f "package.json" ]; then
    print_error "package.json not found in backend directory"
    exit 1
fi

print_status "Installing backend dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install backend dependencies"
    exit 1
fi

print_status "Backend dependencies installed successfully"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Please create one with your production configuration."
    print_status "Creating sample .env file..."
    cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=your_database_name
DB_PORT=3306

# Server Configuration
PORT=3000
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Cookie Configuration
COOKIE_SECRET=your_cookie_secret_key_here

# CORS Configuration
FRONTEND_URL=https://your-frontend-domain.com
EOF
    print_warning "Please edit .env file with your actual configuration before starting the server"
fi

print_status "Backend ready for deployment"
print_status "To start backend server: npm run prod"

# Deploy Frontend
echo ""
print_status "Deploying Frontend..."
cd ../admin-frontend

if [ ! -f "package.json" ]; then
    print_error "package.json not found in frontend directory"
    exit 1
fi

print_status "Installing frontend dependencies..."
npm install

if [ $? -ne 0 ]; then
    print_error "Failed to install frontend dependencies"
    exit 1
fi

print_status "Frontend dependencies installed successfully"

print_status "Building frontend for production..."
npm run build:prod

if [ $? -ne 0 ]; then
    print_error "Failed to build frontend"
    exit 1
fi

print_status "Frontend built successfully!"

# Check if dist folder exists
if [ -d "dist" ]; then
    print_status "Frontend build output available in dist/ folder"
    print_status "You can serve it using: npx serve -s dist -l 3001"
else
    print_error "dist folder not found after build"
    exit 1
fi

echo ""
print_status "🎉 Deployment completed successfully!"
echo ""
print_status "Next steps:"
echo "1. Configure your .env file in backend with production values"
echo "2. Start backend server: cd Pra && npm run prod"
echo "3. Serve frontend: cd admin-frontend && npx serve -s dist -l 3001"
echo "4. Or deploy to your preferred hosting platform"
echo ""
print_status "For detailed instructions, see DEPLOYMENT.md" 