const Subcategory = require("../model/subcategoryModel");

const getAllSubcategories = async (req, res) => {
  try {
    const subcategories = await Subcategory.getAll();
    res.status(200).json({ success: true, data: subcategories });
  } catch (error) {
    console.error("Get all subcategories error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch subcategories", error: error.message });
  }
};

const getByCategoryId = async (req, res) => {
  const { categoryId } = req.params;
  try {
    const subcategories = await Subcategory.getByCategoryId(categoryId);
    res.status(200).json({ success: true, data: subcategories });
  } catch (error) {
    console.error("Get subcategories by category error:", error);
    res.status(500).json({ success: false, message: "Failed to fetch subcategories", error: error.message });
  }
};

const createSubcategory = async (req, res) => {
  try {
    const { name, category_id } = req.body;
    if (!name || !category_id) {
      return res.status(400).json({ success: false, message: "Name and category are required." });
    }
    const id = await Subcategory.create({ name, category_id });
    res.status(201).json({ success: true, data: { id, name, category_id } });
  } catch (error) {
    console.error("Create subcategory error:", error);
    res.status(500).json({ 
      success: false, 
      message: error.code === 'ER_DUP_ENTRY' 
        ? 'This subcategory name already exists.' 
        : 'Failed to create subcategory.',
      error: error.message 
    });
  }
};

const updateSubcategory = async (req, res) => {
  const { id } = req.params;
  const { name, category_id } = req.body;
  try {
     if (!name || !category_id) {
      return res.status(400).json({ success: false, message: "Name and category are required." });
    }
    await Subcategory.update(id, { name, category_id });
    res.json({ success: true, message: "Subcategory updated successfully" });
  } catch (error) {
    console.error("Update subcategory error:", error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update subcategory.',
      error: error.message 
    });
  }
};

const deleteSubcategory = async (req, res) => {
  const { id } = req.params;
  try {
    await Subcategory.delete(id);
    res.json({ success: true, message: "Subcategory deleted" });
  } catch (error) {
    console.error("Delete subcategory error:", error);
    res.status(500).json({ success: false, message: "Failed to delete subcategory", error: error.message });
  }
};

module.exports = {
  getAllSubcategories,
  getByCategoryId,
  createSubcategory,
  updateSubcategory,
  deleteSubcategory,
};
