const { pool } = require('../Config/db');

class OrderModel {
  // Get all orders for admin
  static async getAllOrders(page = 1, limit = 20, status = null) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = '';
      let queryParams = [];

      if (status && status !== 'all') {
        whereClause = 'WHERE o.status = ?';
        queryParams.push(status);
      }

      // Get total count
      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total FROM orders o ${whereClause}`,
        queryParams
      );

      // Get orders with customer details
      const [orders] = await pool.query(
        `SELECT 
          o.*,
          c.name as customer_name,
          c.email as customer_email,
          c.phone as customer_phone,
          COUNT(oi.id) as item_count
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        ${whereClause}
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT ? OFFSET ?`,
        [...queryParams, limit, offset]
      );

      return {
        orders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(countResult[0].total / limit),
          totalOrders: countResult[0].total,
          hasNextPage: page < Math.ceil(countResult[0].total / limit),
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error getting all orders:', error);
      throw error;
    }
  }

  // Get order by ID with full details
  static async getOrderById(orderId) {
    try {
      // Get order details
      const [orders] = await pool.query(
        `SELECT 
          o.*,
          c.name as customer_name,
          c.email as customer_email,
          c.phone as customer_phone
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?`,
        [orderId]
      );

      if (orders.length === 0) {
        return null;
      }

      const order = orders[0];

      // Get order items
      const [items] = await pool.query(
        `SELECT 
          oi.*,
          p.images as product_images
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?`,
        [orderId]
      );

      // Format items with images
      order.items = items.map(item => ({
        ...item,
        product_images: item.product_images ? JSON.parse(item.product_images) : []
      }));

      return order;
    } catch (error) {
      console.error('Error getting order by ID:', error);
      throw error;
    }
  }

  // Get orders by customer ID
  static async getOrdersByCustomer(customerId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      // Get total count
      const [countResult] = await pool.query(
        'SELECT COUNT(*) as total FROM orders WHERE customer_id = ?',
        [customerId]
      );

      // Get orders
      const [orders] = await pool.query(
        `SELECT 
          o.*,
          COUNT(oi.id) as item_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.customer_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT ? OFFSET ?`,
        [customerId, limit, offset]
      );

      return {
        orders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(countResult[0].total / limit),
          totalOrders: countResult[0].total,
          hasNextPage: page < Math.ceil(countResult[0].total / limit),
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error getting customer orders:', error);
      throw error;
    }
  }

  // Update order status
  static async updateOrderStatus(orderId, status, message = null, adminId = null) {
    try {
      // Update order status
      await pool.query(
        'UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, orderId]
      );

      // Add status history if message provided
      if (message) {
        try {
          await pool.query(
            'INSERT INTO order_status_history (order_id, status, message, created_by_admin_id, created_at) VALUES (?, ?, ?, ?, NOW())',
            [orderId, status, message, adminId]
          );
        } catch (historyError) {
          console.warn('Could not add status history:', historyError);
          // Continue even if history fails
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Get order statistics
  static async getOrderStats() {
    try {
      // Get total orders and revenue
      const [totalStats] = await pool.query(
        `SELECT 
          COUNT(*) as totalOrders,
          SUM(total_amount) as totalRevenue,
          AVG(total_amount) as averageOrderValue
        FROM orders`
      );

      // Get status counts
      const [statusCounts] = await pool.query(
        `SELECT 
          status,
          COUNT(*) as count
        FROM orders 
        GROUP BY status`
      );

      // Get recent orders
      const [recentOrders] = await pool.query(
        `SELECT 
          o.*,
          c.name as customer_name
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        ORDER BY o.created_at DESC
        LIMIT 10`
      );

      // Format status counts
      const statusCountsFormatted = {};
      statusCounts.forEach(item => {
        statusCountsFormatted[item.status] = item.count;
      });

      return {
        totalOrders: totalStats[0].totalOrders || 0,
        totalRevenue: totalStats[0].totalRevenue || 0,
        averageOrderValue: totalStats[0].averageOrderValue || 0,
        statusCounts: statusCountsFormatted,
        recentOrders
      };
    } catch (error) {
      console.error('Error getting order stats:', error);
      throw error;
    }
  }

  // Search orders
  static async searchOrders(filters, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = 'WHERE 1=1';
      let queryParams = [];

      if (filters.query) {
        whereClause += ' AND (o.order_number LIKE ? OR c.name LIKE ? OR c.email LIKE ?)';
        const searchTerm = `%${filters.query}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm);
      }

      if (filters.status) {
        whereClause += ' AND o.status = ?';
        queryParams.push(filters.status);
      }

      if (filters.startDate) {
        whereClause += ' AND DATE(o.created_at) >= ?';
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        whereClause += ' AND DATE(o.created_at) <= ?';
        queryParams.push(filters.endDate);
      }

      if (filters.minAmount) {
        whereClause += ' AND o.total_amount >= ?';
        queryParams.push(filters.minAmount);
      }

      if (filters.maxAmount) {
        whereClause += ' AND o.total_amount <= ?';
        queryParams.push(filters.maxAmount);
      }

      // Get total count
      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total 
         FROM orders o 
         LEFT JOIN customers c ON o.customer_id = c.id 
         ${whereClause}`,
        queryParams
      );

      // Get orders
      const [orders] = await pool.query(
        `SELECT 
          o.*,
          c.name as customer_name,
          c.email as customer_email,
          COUNT(oi.id) as item_count
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        ${whereClause}
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT ? OFFSET ?`,
        [...queryParams, limit, offset]
      );

      return {
        orders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(countResult[0].total / limit),
          totalOrders: countResult[0].total,
          hasNextPage: page < Math.ceil(countResult[0].total / limit),
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error searching orders:', error);
      throw error;
    }
  }

  // Export orders
  static async exportOrders(filters = {}) {
    try {
      let whereClause = 'WHERE 1=1';
      let queryParams = [];

      if (filters.status) {
        whereClause += ' AND o.status = ?';
        queryParams.push(filters.status);
      }

      if (filters.startDate) {
        whereClause += ' AND DATE(o.created_at) >= ?';
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        whereClause += ' AND DATE(o.created_at) <= ?';
        queryParams.push(filters.endDate);
      }

      const [orders] = await pool.query(
        `SELECT 
          o.*,
          c.name as customer_name,
          c.email as customer_email,
          c.phone as customer_phone
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        ${whereClause}
        ORDER BY o.created_at DESC`,
        queryParams
      );

      return orders;
    } catch (error) {
      console.error('Error exporting orders:', error);
      throw error;
    }
  }
}

module.exports = OrderModel;
