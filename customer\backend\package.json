{"name": "ecommerce-customer-backend", "version": "1.0.0", "description": "Ecommerce customer module backend (MVC structured)", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "build": "echo 'No build step required for this Node.js backend'"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "nodemailer": "^7.0.4", "razorpay": "^2.9.6", "twilio": "^5.7.1", "validator": "^13.15.15"}, "devDependencies": {"nodemon": "^3.1.10"}}