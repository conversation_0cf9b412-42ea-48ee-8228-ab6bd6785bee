const express = require('express');
const router = express.Router();
const { createOrder, verifyPayment, getPaymentDetails } = require('../controllers/paymentController');
const authenticateToken = require('../middleware/customerAuth');

// Create Razorpay order
router.post('/create-order', authenticateToken, createOrder);

// Verify Razorpay payment
router.post('/verify', authenticateToken, verifyPayment);

// Get payment details for an order
router.get('/order/:order_id', authenticateToken, getPaymentDetails);

module.exports = router;